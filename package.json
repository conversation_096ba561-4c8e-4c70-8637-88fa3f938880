{"name": "landingpagenow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "e2e": "dotenv -e .env.test -c -- playwright test", "e2e:ui": "dotenv -e .env.test -c -- playwright test --project=chromium --ui", "e2e:desktop": "dotenv -e .env.test -c -- playwright test --project=chromium", "e2e:mobile": "dotenv -e .env.test -c -- playwright test --project=mobile-chrome", "e2e:a11y": "dotenv -e .env.test -c -- playwright test --project=accessibility", "postinstall": "npx playwright install chromium"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "js-beautify": "^1.15.4", "lucide-react": "^0.503.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-syntax-highlighter": "^15.6.1", "react-turnstile": "^1.1.4", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "zod": "^3.24.3"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/js-beautify": "^1.14.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.2", "@vitest/ui": "^3.1.2", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.0", "jsdom": "^26.1.0", "msw": "^2.7.5", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5", "vitest": "^3.1.2"}}