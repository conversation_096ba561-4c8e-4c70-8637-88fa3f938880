export interface UnsplashConfig {
    accessKey: string;
    applicationName: string;
}

export interface UnsplashSearchOptions {
    query: string;
    page?: number;
    perPage?: number;
    orientation?: "landscape" | "portrait" | "squarish";
}

export interface UnsplashImage {
    id: string;
    width: number;
    height: number;
    description: string | null;
    urls: {
        raw: string;
        full: string;
        regular: string;
        small: string;
        thumb: string;
    };
    links: {
        self: string;
        html: string;
        download: string;
    };
}

export interface UnsplashSearchResponse {
    total: number;
    total_pages: number;
    results: UnsplashImage[];
}

export class UnsplashError extends Error {
    constructor(
        message: string,
        public readonly status?: number,
    ) {
        super(message);
        this.name = "UnsplashError";
    }
}
