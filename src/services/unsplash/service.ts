import {
    UnsplashConfig,
    UnsplashError,
    UnsplashImage,
    UnsplashSearchOptions,
    UnsplashSearchResponse,
} from "./types";

export class UnsplashService {
    private readonly baseUrl = "https://api.unsplash.com";
    private readonly defaultOptions: Required<UnsplashSearchOptions> = {
        query: "",
        page: 1,
        perPage: 10,
        orientation: "landscape",
    };

    constructor(private readonly config: UnsplashConfig) {}

    /**
     * Search for images based on query and options
     */
    async searchImages(
        options: UnsplashSearchOptions,
    ): Promise<UnsplashSearchResponse> {
        const searchOptions = { ...this.defaultOptions, ...options };
        const url = new URL(`${this.baseUrl}/search/photos`);

        url.searchParams.append("query", searchOptions.query);
        url.searchParams.append("page", String(searchOptions.page));
        url.searchParams.append("per_page", String(searchOptions.perPage));
        url.searchParams.append("orientation", searchOptions.orientation);

        try {
            const response = await fetch(url.toString(), {
                headers: {
                    Authorization: `Client-ID ${this.config.accessKey}`,
                    "Accept-Version": "v1",
                    "User-Agent": this.config.applicationName,
                },
            });

            if (!response.ok) {
                throw new UnsplashError(
                    `Unsplash API error: ${response.statusText}`,
                    response.status,
                );
            }

            return response.json();
        } catch (error) {
            if (error instanceof UnsplashError) {
                throw error;
            }
            throw new UnsplashError(
                error instanceof Error ? error.message : "Unknown error",
            );
        }
    }

    /**
     * Get a random image URL based on search query
     */
    async getRandomImageUrl(query: string): Promise<string> {
        const response = await this.searchImages({
            query,
            perPage: 1,
            page: Math.floor(Math.random() * 10) + 1,
        });

        if (response.results.length === 0) {
            throw new UnsplashError("No images found for query: " + query);
        }

        return response.results[0].urls.regular;
    }

    /**
     * Extract potential keywords from text content
     */
    extractKeywords(content: string): string[] {
        // Remove HTML tags and special characters
        const cleanContent = content.replace(/<[^>]*>/g, " ")
            .replace(/[^\w\s]/g, " ")
            .toLowerCase();

        // Split into words and remove common words
        const words = cleanContent.split(/\s+/).filter((word) =>
            word.length > 3 && !this.isCommonWord(word)
        );

        // Get unique words sorted by frequency
        const wordFreq = words.reduce((acc, word) => {
            acc[word] = (acc[word] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        return Object.entries(wordFreq)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([word]) => word);
    }

    private isCommonWord(word: string): boolean {
        const commonWords = new Set([
            "the",
            "be",
            "to",
            "of",
            "and",
            "a",
            "in",
            "that",
            "have",
            "this",
            "for",
            "not",
            "with",
            "you",
            "do",
            "but",
            "from",
            "they",
            "which",
            "one",
            "would",
            "what",
            "all",
            "will",
            "there",
            "say",
            "who",
            "make",
            "when",
            "can",
            "more",
            "if",
            "no",
            "man",
            "out",
            "other",
            "than",
            "now",
            "into",
            "its",
            "time",
            "that",
            "just",
            "look",
            "like",
            "could",
            "page",
            "find",
            "also",
            "back",
            "any",
            "then",
            "some",
            "take",
            "only",
        ]);
        return commonWords.has(word);
    }
}

/**
 * Default instance for easy import
 */
export const unsplash = new UnsplashService({
    accessKey: process.env.UNSPLASH_ACCESS_KEY || "",
    applicationName: "Landing Page Now",
});
