import { beforeEach, describe, expect, it, vi } from "vitest";
import { UnsplashService } from "../service";
import { UnsplashError } from "../types";

describe("UnsplashService", () => {
    let service: UnsplashService;

    beforeEach(() => {
        service = new UnsplashService({
            accessKey: "test-key",
            applicationName: "test-app",
        });
        vi.clearAllMocks();
    });

    describe("searchImages", () => {
        it("should return search results for successful response", async () => {
            const result = await service.searchImages({ query: "test" });

            expect(result).toEqual({
                total: 1,
                total_pages: 1,
                results: [
                    {
                        urls: {
                            regular: "test-image-url",
                        },
                    },
                ],
            });
        });

        it("should throw UnsplashError for failed response", async () => {
            await expect(service.searchImages({ query: "unauthorized" }))
                .rejects.toThrow(
                    UnsplashError,
                );
        });
    });

    describe("getRandomImageUrl", () => {
        it("should return image URL from search results", async () => {
            const url = await service.getRandomImageUrl("test");
            expect(url).toBe("test-image-url");
        });

        it("should throw error when no images found", async () => {
            await expect(service.getRandomImageUrl("nonexistent")).rejects
                .toThrow(
                    "No images found for query: nonexistent",
                );
        });
    });

    describe("extractKeywords", () => {
        it("should extract keywords from HTML content", () => {
            const content = `
                <div>
                    <h1>Business Strategy Meeting</h1>
                    <p>Join us for a business conference about digital business transformation.</p>
                    <img src="meeting.jpg" alt="Business meeting" />
                </div>
            `;

            const keywords = service.extractKeywords(content);
            expect(keywords).toContain("business");
            expect(keywords.length).toBeLessThanOrEqual(5);
            expect(keywords.every((word) => word.length > 3)).toBe(true);
        });

        it("should ignore common words and short words", () => {
            const content = "The quick brown fox jumps over the lazy dog";
            const keywords = service.extractKeywords(content);
            expect(keywords).not.toContain("the");
            expect(keywords).not.toContain("fox");
            expect(keywords.length).toBeLessThanOrEqual(5);
        });

        it("should handle empty content", () => {
            const keywords = service.extractKeywords("");
            expect(keywords).toEqual([]);
        });
    });
});
