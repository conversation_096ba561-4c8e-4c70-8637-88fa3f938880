import { PageConfig } from "@/types";

/**
 * Page generator service configuration
 */
export interface PageGeneratorConfig {
    /** Maximum length of generated HTML content */
    maxContentLength?: number;
    /** Default language for generation */
    defaultLanguage?: string;
    /** Default style for generation */
    defaultStyle?: string;
    /** OpenRouter API model to use */
    model?: string;
    /** Reasoning configuration */
    reasoning?: {
        /** Whether to exclude reasoning from the output */
        exclude?: boolean;
    };
}

/**
 * Supported page types
 */
export enum PageType {
    LEAD_GENERATION = "lead-generation",
    SALES = "sales",
    PRODUCT = "product",
    WEBINAR = "webinar",
    EVENT = "event",
}

/**
 * Supported page styles
 */
export enum PageStyle {
    MODERN = "modern",
    MINIMAL = "minimal",
    BOLD = "bold",
    ELEGANT = "elegant",
    PLAYFUL = "playful",
}

/**
 * Supported languages
 */
export enum PageLanguage {
    ENGLISH = "en",
    POLISH = "pl",
    GERMAN = "de",
    FRENCH = "fr",
    SPANISH = "es",
}

/**
 * Generation options for page generator
 */
export interface PageGenerationOptions {
    /** Product/Service/Topic name */
    productName: string;
    /** Product/Service/Topic description */
    productDescription: string;
    /** Target audience description */
    targetAudience: string;
    /** Keywords for the page */
    keywords: string[];
    /** Type of the page */
    type: PageType;
    /** Style of the page */
    style: PageStyle;
    /** Language of the page */
    language: string;
    /** Optional advanced requirements */
    additionalRequirements?: string;
    /** Optional integration code */
    integrationCode?: string;
    /** Optional integrations */
    integrations?: {
        leadCapture?: string;
    };
}

/**
 * Result of page generation
 */
export interface PageGenerationResult {
    /** Generated HTML content */
    html: string;
    /** Page configuration */
    config: PageConfig;
    /** Generation duration in milliseconds */
    generationDurationMs: number;
    /** Tokens used for generation */
    tokens?: {
        prompt: number;
        completion: number;
        total: number;
    };
    /** Cost information for the generation */
    cost?: {
        promptCost: number;
        completionCost: number;
        totalCost: number;
        currency: string;
    };
    /** Model used for generation */
    model?: string;
    /** Whether the HTML is partially generated */
    isPartial?: boolean;
    /** Total number of attempts made to generate the page */
    totalAttempts?: number;
    /** Cumulative cost information across all attempts */
    cumulativeCost?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
        promptCost: number;
        completionCost: number;
        totalCost: number;
        currency: string;
    };
}

/**
 * Error during page generation
 */
export class PageGenerationError extends Error {
    constructor(
        message: string,
        public readonly code: string,
        public readonly details?: unknown,
    ) {
        super(message);
        this.name = "PageGenerationError";
    }
}

/**
 * Maps between PageGenerationOptions and PageConfig
 */
export function mapToPageConfig(options: PageGenerationOptions): PageConfig {
    return {
        type: options.type,
        targetAudience: options.targetAudience,
        keywords: options.keywords,
        style: options.style,
        language: options.language,
        integrations: {
            leadCapture: options.integrations?.leadCapture ?? "",
        },
    };
}
