import {
   // PageGenerationOptions, // Removed as unused
   // PageLanguage, // Removed as unused
   // PageStyle, // Removed as unused
   // PageType, // Removed as unused
} from "./types";

const iconLibraries = [
   "Heroicons",
   "Flowbite",
   "Lucide",
   "Phosphor Icons",
   "Tabler Icons",
];

const randomIconLibrary =
   iconLibraries[Math.floor(Math.random() * iconLibraries.length)];

/**
 * Constructs a system prompt for the AI
 */
export function getSystemPrompt(): string {
   return `You are an expert web developer creating complete HTML landing pages.

CRITICAL OUTPUT REQUIREMENTS:
1. Output MUST be a complete HTML document starting with <!DOCTYPE html> and ending with </html>
2. Output MUST be raw HTML code ONLY - no markdown code blocks, no explanations, no reasoning, no thinking process
3. DO NOT output any planning, color analysis, or design considerations - ONLY HTML CODE
4. Start your response immediately with <!DOCTYPE html> - no other text before it
5. Every opening tag MUST have a matching closing tag
6. Final code should be minified (remove unnecessary whitespace) to save tokens
7. Final code should be a valid HTML document that can be opened directly in browser

TECHNICAL REQUIREMENTS:
1. Use Tailwind CSS via CDN: <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
2. Include Alpine.js for interactivity: <script src="https://unpkg.com/alpinejs" defer></script>. IMPORTANT: Do not use Alpine.js for anything other than interactivity, we don't need it for styling. Make sure it's included before closing </body> tag.
3. Use semantic HTML5 elements for accessibility
4. Ensure WCAG 2.1 accessibility compliance
5. Mobile-first responsive design
6. Images from images.unsplash.com (not deprecated source.unsplash.com)
7. Icons from ${randomIconLibrary} (SVG format)
8. Include proper meta tags for SEO and viewport

STYLING GUIDELINES:
1. Use only Tailwind CSS utility classes for styling
2. Create custom CSS variables for colors in <head> section if needed
3. Color psychology matching product/service (blue for trust/tech, green for health/finance, etc.)
4. Modern ${new Date().getFullYear()} design trends
5. Subtle animations where appropriate
6. Add overflow-y-scroll class to <body> element to always have scrollbar visible
7. Add smooth-scroll class to <html> element to enable smooth scrolling

CRITICAL: Output ONLY minified HTML code starting with <!DOCTYPE html> - no explanations, no reasoning, no planning - ONLY HTML CODE.`;
}

/**
 * Constructs the main prompt for generating landing page HTML
 */
export function getGenerationPrompt(productDetails: {
   product_name: string;
   target_group: string;
   keywords: string;
   style: string;
   additional_requirements?: string;
   integration_code?: string;
}): string {
   return `Create a complete HTML landing page for the following product/service:

<product_name>${productDetails.product_name}</product_name>
<target_group>${productDetails.target_group}</target_group>
<keywords>${productDetails.keywords}</keywords>
${
      productDetails.additional_requirements
         ? `<additional_requirements>${productDetails.additional_requirements}</additional_requirements>`
         : ""
   }
${
      productDetails.integration_code
         ? `<integration_code>${productDetails.integration_code}</integration_code>`
         : ""
   }

STRUCTURE & SEO REQUIREMENTS:
1. Complete HTML document with DOCTYPE, html, head, and body tags
2. Proper meta tags including viewport, charset, and SEO optimization
3. SEO-optimized title (50-60 characters) that includes main keyword
4. Compelling meta description (150-160 characters) with call-to-action
5. 8-12 relevant meta keywords based on product and target audience
6. Include Tailwind CSS and Alpine.js via CDN

REQUIRED LANDING PAGE SECTIONS:
1. Hero Section: Attention-grabbing headline, 3 main benefits, clear CTA
2. Benefits Section: Top 4 benefits/features with competitive advantages
3. Social Proof: Customer reviews, testimonials, star ratings
4. Trust Building: Return policy, guarantees, expert endorsements
5. FAQ Section: 5-8 strategic questions and answers
6. Final CTA: Strong call-to-action with urgency/scarcity

CONVERSION OPTIMIZATION:
- Multiple CTAs throughout (beginning, middle, end)
- Price, delivery promise, and purchase button in close proximity
- Risk reversal elements (guarantees, return policy, support)
- Free valuable content offers where appropriate
- Mobile-optimized with sticky elements if applicable

DESIGN REQUIREMENTS:
1. ${productDetails.style} design aesthetic fitting ${
      new Date().getFullYear()
   } trends
2. Theme colors using <style type="text/tailwindcss">@theme directive in head
3. Separate <style></style> block for additional CSS (AVOID @apply and @imports)
4. Psychology-aligned color scheme using @theme directive following this EXACT format:
   <style type="text/tailwindcss">
      @theme {
         --color-primary: HEX_COLOR_HERE;
         --color-secondary: HEX_COLOR_HERE;
         --color-accent: HEX_COLOR_HERE;
      }  
   </style>
   where HEX_COLOR_HERE is a valid hex color code (e.g., #000000, #ffffff, #000000, etc.)
5. High-quality typography and visual hierarchy. Use vertical rhythm approach and consistent spacing.
6. Responsive design for all devices.
7. Smooth scrolling and modern animations.
8. Use Tailwind CSS utility classes for styling.
9. Use Alpine.js for interactivity.
10. Use Tailwind CSS and Alpine.js via CDN.
11. IMPORTANT: Make sure text is readable and accessible, we don't want white text on white background, etc.

CONTENT GUIDELINES:
1. Maximum 5 bullet points per section
2. Logical information flow: benefits → proof → offer → action
3. Address common objections proactively
4. Avoid overwhelming users with too many options

Output a complete, production-ready HTML document that can be opened directly in a browser. The HTML should be MINIFIED to save tokens while maintaining functionality.

CRITICAL: Start your response immediately with <!DOCTYPE html> and output only the MINIFIED HTML code - no explanations, no markdown blocks, no additional text, no reasoning, no planning. Begin with <!DOCTYPE html> right now.`;
}

export function getSEOMetadataPrompt(productDetails: {
   product_name: string;
   target_group: string;
   keywords: string;
}): string {
   return `Create SEO-optimized page title and meta description for this product/service:

<product_service_description>${productDetails.product_name}</product_service_description>
<target_group>${productDetails.target_group}</target_group>
<keywords>${productDetails.keywords}</keywords>

REQUIREMENTS:
1. Title: 50-60 characters, include main keyword, compelling format
2. Description: 150-160 characters, compelling with CTA, accurate representation
3. Natural keyword integration, avoid keyword stuffing
4. Make content click-worthy and search engine friendly

PROCESS (wrap in <seo_optimization_process> tags):
1. List key phrases and potential keywords from the description
2. Brainstorm 3 potential titles and 3 potential descriptions
3. Count characters for each (prepend numbers when counting)
4. Evaluate based on SEO best practices

OUTPUT FORMAT:
{
  "title": "Your SEO-Optimized Title Here",
  "description": "Your SEO-Optimized Meta Description Here"
}

Return ONLY the JSON object without any additional text or explanation.`;
}
