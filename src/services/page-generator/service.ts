import {
    ChatCompletionResponse,
    createOpenRouterService,
    OpenRouterService,
} from "../openrouter";
import { getGenerationPrompt, getSystemPrompt } from "./prompts";
import {
    mapToPageConfig,
    PageGenerationError,
    PageGenerationOptions,
    PageGenerationResult,
    PageGeneratorConfig,
} from "./types";
import { formatHtml, processHtml } from "./html-processor";

/**
 * Service for generating landing pages using AI
 */
export class PageGeneratorService {
    private readonly openRouter: OpenRouterService;
    private readonly config: Required<PageGeneratorConfig>;

    /**
     * Default configuration values
     */
    private static readonly DEFAULT_CONFIG: Required<PageGeneratorConfig> = {
        maxContentLength: 1024 * 1024, // 1MB
        defaultLanguage: "English",
        defaultStyle: "modern",
        model: process.env.OPENROUTER_DEFAULT_MODEL ??
            "anthropic/claude-3-haiku",
        reasoning: {
            exclude: true,
        },
    };

    /**
     * Creates a new PageGeneratorService
     */
    constructor(
        private readonly apiKey: string,
        config: PageGeneratorConfig = {},
    ) {
        if (!apiKey) {
            throw new Error("API key is required for PageGeneratorService");
        }

        // Merge default config with provided config
        this.config = { ...PageGeneratorService.DEFAULT_CONFIG, ...config };

        // Initialize OpenRouter service
        this.openRouter = createOpenRouterService({
            apiKey,
            defaultModel: this.config.model,
            reasoning: this.config.reasoning,
        });
    }

    /**
     * Validates generation options
     */
    private validateOptions(options: PageGenerationOptions): void {
        const { productName, productDescription, targetAudience, keywords } =
            options;

        if (!productName || productName.trim().length < 3) {
            throw new PageGenerationError(
                "Product name is required and must be at least 3 characters",
                "INVALID_PRODUCT_NAME",
            );
        }

        if (!productDescription || productDescription.trim().length < 10) {
            throw new PageGenerationError(
                "Product description is required and must be at least 10 characters",
                "INVALID_PRODUCT_DESCRIPTION",
            );
        }

        if (!targetAudience || targetAudience.trim().length < 5) {
            throw new PageGenerationError(
                "Target audience is required and must be at least 5 characters",
                "INVALID_TARGET_AUDIENCE",
            );
        }

        if (
            !keywords?.length ||
            !keywords.some((k) => k.trim().length > 0)
        ) {
            throw new PageGenerationError(
                "At least one keyword is required",
                "INVALID_KEYWORDS",
            );
        }
    }

    /**
     * Validates HTML content
     */
    private validateHtml(html: string, context: string = "unknown"): boolean {
        console.log(`[DEBUG] Starting HTML validation - Context: ${context}`);
        console.log("[DEBUG] HTML length:", html.length);
        console.log("[DEBUG] HTML starts with:", html.slice(0, 50));
        console.log("[DEBUG] HTML ends with:", html.slice(-50));

        // Now we expect complete HTML documents from the LLM
        // Check for required HTML5 elements
        const requiredElements = [
            "<!DOCTYPE html>",
            "<html",
            "</html>",
            "<head",
            "</head>",
            "<body",
            "</body>",
        ];

        // Check if all required elements are present
        console.log(
            `[DEBUG] ${context} - Checking for required HTML elements...`,
        );
        const missingElements = requiredElements.filter((el) => {
            const found = html.includes(el);
            console.log(
                `[DEBUG] ${context} - Element "${el}": ${
                    found ? "FOUND" : "MISSING"
                }`,
            );
            return !found;
        });

        if (missingElements.length > 0) {
            console.log(
                `[DEBUG] ${context} - Missing HTML elements:`,
                missingElements,
            );
            return false;
        }

        return true;
    }

    private async generateWithRetries(
        options: PageGenerationOptions,
        maxAttempts: number,
        openRouter: OpenRouterService,
    ): Promise<{
        output: string;
        completionResponse: ChatCompletionResponse;
        generationIds: string[];
        totalCostTracking: {
            promptTokens: number;
            completionTokens: number;
            totalTokens: number;
            promptCost: number;
            completionCost: number;
            totalCost: number;
            attempts: number;
        };
    }> {
        let output = "";
        let completionResponse: ChatCompletionResponse | null = null;
        let attempts = 0;
        const generationIds: string[] = [];
        const totalCostTracking = {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0,
            promptCost: 0,
            completionCost: 0,
            totalCost: 0,
            attempts: 0,
        };
        const productDetails = {
            product_name: options.productName,
            target_group: options.targetAudience,
            keywords: options.keywords.join(", "),
            style: options.style.toString(),
            additional_requirements: options.additionalRequirements ?? "",
            integration_code: options.integrationCode ?? "",
        };
        while (attempts < maxAttempts) {
            attempts++;

            const systemPrompt = getSystemPrompt();
            const userPrompt = getGenerationPrompt(productDetails);
            let messagesForLlm: Array<
                { role: "system" | "user" | "assistant"; content: string }
            > = [];
            if (attempts === 1 || !output) {
                messagesForLlm = [
                    { role: "system", content: systemPrompt },
                    { role: "user", content: userPrompt },
                ];
            } else {
                const retryPrompt =
                    "The HTML you provided above (in the assistant message) is incomplete or was invalid. " +
                    "Please continue generating ONLY the additional HTML needed to complete the document based on the original user prompt. " +
                    "Do NOT repeat any of the HTML you already provided in the assistant message. " +
                    "If the document already has DOCTYPE, html, head, or body tags, do not add them again. " +
                    "Focus on appending the missing parts to make it a single, complete, and valid HTML document.";
                messagesForLlm = [
                    { role: "system", content: systemPrompt },
                    { role: "user", content: userPrompt },
                    { role: "assistant", content: output },
                    { role: "user", content: retryPrompt },
                ];
            }
            const requestParams = {
                messages: messagesForLlm,
                temperature: 1,
                max_tokens: 4096,
                reasoning: { exclude: true },
            };

            const response = await openRouter.createChatCompletion(
                requestParams,
            );
            if (Symbol.asyncIterator in Object(response)) {
                throw new PageGenerationError(
                    "Streaming responses are not supported",
                    "STREAMING_NOT_SUPPORTED",
                );
            }
            completionResponse = response as ChatCompletionResponse;
            const currentLlmAttemptContent =
                completionResponse.choices[0].message.content;

            output += currentLlmAttemptContent;

            if (
                !output.trim().startsWith("<!DOCTYPE html>") &&
                output.includes("<!DOCTYPE html>")
            ) {
                const doctypeIndex = output.indexOf("<!DOCTYPE html>");
                const htmlEndIndex = output.lastIndexOf("</html>");

                if (doctypeIndex !== -1 && htmlEndIndex !== -1) {
                    const extractedHtml = output.substring(
                        doctypeIndex,
                        htmlEndIndex + 7,
                    ); // +7 for "</html>"
                    output = extractedHtml;
                }
            }

            // For continuation attempts, ensure we don't duplicate content
            if (attempts > 1 && output.includes("<!DOCTYPE html>")) {
                const doctypeOccurrences =
                    (output.match(/<!DOCTYPE html>/g) || []).length;
                if (doctypeOccurrences > 1) {
                    // Keep only the first complete part
                    const firstDoctype = output.indexOf("<!DOCTYPE html>");
                    const secondDoctype = output.indexOf(
                        "<!DOCTYPE html>",
                        firstDoctype + 1,
                    );
                    if (secondDoctype !== -1) {
                        output = output.substring(firstDoctype, secondDoctype);
                    }
                }
            }

            if (completionResponse.id) {
                generationIds.push(completionResponse.id);
            }
            const tokenUsage = {
                prompt: completionResponse.usage.prompt_tokens,
                completion: completionResponse.usage.completion_tokens,
                total: completionResponse.usage.total_tokens,
            };
            totalCostTracking.attempts++;
            totalCostTracking.promptTokens += tokenUsage.prompt;
            totalCostTracking.completionTokens += tokenUsage.completion;
            totalCostTracking.totalTokens += tokenUsage.total;
            if (output.length > this.config.maxContentLength) {
                throw new PageGenerationError(
                    `Generated content exceeds maximum length (${output.length} > ${this.config.maxContentLength})`,
                    "CONTENT_TOO_LARGE",
                    output,
                );
            }

            const isValidHtml = this.validateHtml(output);

            if (!isValidHtml) {
                continue;
            }

            return {
                output,
                completionResponse,
                generationIds,
                totalCostTracking,
            };
        }
        throw new PageGenerationError(
            "Failed to generate valid HTML after maximum attempts",
            "MAX_ATTEMPTS_REACHED",
        );
    }

    private async calculateTotalCost(
        generationIds: string[],
        totalCostTracking: {
            promptTokens: number;
            completionTokens: number;
            totalTokens: number;
            promptCost: number;
            completionCost: number;
            totalCost: number;
            attempts: number;
        },
        openRouter: OpenRouterService,
    ): Promise<number> {
        let totalActualCost = 0;
        let allDetailsFailed = true;

        for (const genId of generationIds) {
            try {
                const genDetails = await openRouter.getGenerationDetails(genId);
                if (genDetails.total_cost !== undefined) {
                    totalActualCost += genDetails.total_cost;
                    allDetailsFailed = false;
                }
            } catch (error) {
                console.error(
                    `Failed to fetch generation details for ${genId}:`,
                    error,
                );
                // Continue with other IDs if one fails
            }
        }

        if (allDetailsFailed) {
            const inputCostPer1k = 0.003;
            const outputCostPer1k = 0.015;
            const estimatedCost =
                (totalCostTracking.promptTokens / 1000) * inputCostPer1k +
                (totalCostTracking.completionTokens / 1000) * outputCostPer1k;
            totalActualCost = estimatedCost;
        }

        return totalActualCost;
    }

    /**
     * Generates a landing page based on the provided options
     */
    async generatePage(
        options: PageGenerationOptions,
    ): Promise<PageGenerationResult> {
        try {
            this.validateOptions(options);
            const startTime = Date.now();
            const maxAttempts = 10; // Increase to 10 for proper HTML completion
            const {
                output,
                completionResponse,
                generationIds,
                totalCostTracking,
            } = await this.generateWithRetries(
                options,
                maxAttempts,
                this.openRouter,
            );
            const processedHtml = processHtml(output);
            const formattedHtml = formatHtml(processedHtml);
            const totalActualCost = await this.calculateTotalCost(
                generationIds,
                totalCostTracking,
                this.openRouter,
            );
            totalCostTracking.totalCost = totalActualCost;
            return {
                html: formattedHtml,
                config: mapToPageConfig(options),
                generationDurationMs: Date.now() - startTime,
                tokens: {
                    prompt: completionResponse.usage.prompt_tokens,
                    completion: completionResponse.usage.completion_tokens,
                    total: completionResponse.usage.total_tokens,
                },
                cost: {
                    promptCost: 0,
                    completionCost: 0,
                    totalCost: totalActualCost,
                    currency: "USD",
                },
                model: completionResponse.model,
                isPartial: false,
                totalAttempts: totalCostTracking.attempts,
                cumulativeCost: {
                    promptTokens: totalCostTracking.promptTokens,
                    completionTokens: totalCostTracking.completionTokens,
                    totalTokens: totalCostTracking.totalTokens,
                    promptCost: 0,
                    completionCost: 0,
                    totalCost: totalActualCost,
                    currency: "USD",
                },
            };
        } catch (error) {
            if (error instanceof PageGenerationError) {
                throw error;
            }
            throw new PageGenerationError(
                `Failed to generate page: ${
                    error instanceof Error ? error.message : String(error)
                }`,
                "GENERATION_FAILED",
                error,
            );
        }
    }
}

/**
 * Creates a new PageGeneratorService
 */
export function createPageGeneratorService(
    apiKey: string,
    config?: PageGeneratorConfig,
): PageGeneratorService {
    return new PageGeneratorService(apiKey, config);
}
