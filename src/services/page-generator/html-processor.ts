/**
 * HTML processing utilities for the page generator
 */

import { html as beautifyHtml } from "js-beautify";

/**
 * Processes HTML content by validating and formatting it
 * Now expects complete HTML documents from the LLM
 */
export function processHtml(htmlInput: string): string {
    // The LLM should now provide complete HTML documents
    // We just need to validate and format them

    // Basic validation - ensure it's a complete HTML document
    if (!htmlInput.includes("<!DOCTYPE") || !htmlInput.includes("</html>")) {
        throw new Error(
            "Invalid HTML: Expected complete HTML document with DOCTYPE and closing html tag",
        );
    }

    // Return the HTML as-is since it should be complete
    return htmlInput.trim();
}

/**
 * Extracts just the body content from a complete HTML document
 */
export function extractBodyContent(html: string): string | null {
    const bodyStartRegex = /<body[^>]*>/i;
    const bodyEndRegex = /<\/body>/i;

    const bodyStartMatch = html.match(bodyStartRegex);
    const bodyEndMatch = html.match(bodyEndRegex);

    if (!bodyStartMatch || !bodyEndMatch) {
        return null;
    }

    const bodyStartIndex = bodyStartMatch.index! + bodyStartMatch[0].length;
    const bodyEndIndex = bodyEndMatch.index!;

    return html.substring(bodyStartIndex, bodyEndIndex).trim();
}

/**
 * Formats minified HTML by adding proper indentation and line breaks
 * This makes the HTML more readable when stored in the database
 */
export function formatHtml(html: string): string {
    return beautifyHtml(html, {
        indent_size: 2,
        indent_char: " ",
        max_preserve_newlines: 2,
        preserve_newlines: true,
        indent_scripts: "normal",
        end_with_newline: true,
        wrap_line_length: 0,
        indent_inner_html: true,
        unformatted: ["pre", "code"],
        content_unformatted: ["pre", "textarea"],
    });
}
