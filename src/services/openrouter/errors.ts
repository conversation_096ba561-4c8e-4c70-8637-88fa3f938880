export class OpenRouterError extends <PERSON>rror {
    constructor(
        message: string,
        public readonly code: string,
        public readonly statusCode?: number,
        public readonly cause?: Error,
    ) {
        super(message);
        this.name = "OpenRouterError";
    }
}

export class ApiKeyError extends OpenRouterError {
    constructor(message: string, cause?: Error) {
        super(message, "API_KEY_ERROR", 401, cause);
        this.name = "ApiKeyError";
    }
}

export class RateLimitError extends OpenRouterError {
    constructor(message: string, cause?: Error) {
        super(message, "RATE_LIMIT_ERROR", 429, cause);
        this.name = "RateLimitError";
    }
}

export class ValidationError extends OpenRouterError {
    constructor(message: string, cause?: Error) {
        super(message, "VALIDATION_ERROR", 400, cause);
        this.name = "ValidationError";
    }
}

export class NetworkError extends OpenRouterError {
    constructor(message: string, cause?: Error) {
        super(message, "NETWORK_ERROR", 503, cause);
        this.name = "NetworkError";
    }
}
