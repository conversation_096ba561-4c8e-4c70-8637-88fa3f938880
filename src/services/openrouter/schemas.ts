import { z } from "zod";

export const configSchema = z.object({
    apiKey: z.string().min(1, "API key is required"),
    baseUrl: z.string().url().optional(),
    defaultModel: z.string().optional(),
    maxRetries: z.number().int().min(0).max(10).optional(),
    timeout: z.number().int().min(1000).max(300000).optional(), // 1s to 5min
});

export const chatMessageSchema = z.object({
    role: z.enum(["system", "user", "assistant"]),
    content: z.string().min(1, "Message content cannot be empty"),
});

export const jsonSchemaFormat = z.object({
    type: z.literal("json_schema"),
    json_schema: z.object({
        name: z.string(),
        strict: z.literal(true), // Must be true as per implementation plan
        schema: z.record(z.unknown()),
    }),
});

export const chatCompletionRequestSchema = z.object({
    messages: z.array(chatMessageSchema).min(
        1,
        "At least one message is required",
    ),
    model: z.string().min(1, "Model identifier is required"),
    temperature: z.number().min(0).max(2).optional(),
    max_tokens: z.number().int().min(1).max(32000).optional(),
    response_format: jsonSchemaFormat.optional(),
});

export const modelSchema = z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    context_length: z.number().int().min(1),
    pricing: z.object({
        prompt: z.number(),
        completion: z.number(),
    }),
});

export const chatCompletionResponseSchema = z.object({
    id: z.string(),
    object: z.string(),
    choices: z.array(z.object({
        message: chatMessageSchema,
        finish_reason: z.string(),
        index: z.number(),
    })),
    model: z.string(),
    created: z.number(),
    usage: z.object({
        prompt_tokens: z.number().int(),
        completion_tokens: z.number().int(),
        total_tokens: z.number().int(),
    }),
});
