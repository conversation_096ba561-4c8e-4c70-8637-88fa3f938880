import {
    ApiKeyError,
    ChatCompletionRequest,
    ChatCompletionResponse,
    Model,
    NetworkError,
    OpenRouterConfig,
    RateLimitError,
    ValidationError,
} from "./types";
import { handleNetworkError, sanitizeContent } from "./utils";

/**
 * OpenRouter API client
 * Handles the direct communication with the OpenRouter API
 */
export class OpenRouterClient {
    private readonly baseUrl: string;
    private readonly apiKey: string;
    private readonly timeout: number;

    /**
     * Creates a new OpenRouter client
     * @param config Configuration options
     */
    constructor(config: OpenRouterConfig) {
        this.apiKey = config.apiKey;
        this.baseUrl = config.baseUrl ?? "https://openrouter.ai/api/v1";
        this.timeout = config.timeout ?? 30000; // 30 seconds default timeout
    }

    /**
     * Makes a request to the OpenRouter API
     * @param endpoint API endpoint
     * @param method HTTP method
     * @param body Request body
     * @returns Response data
     */
    private async makeRequest<T>(
        endpoint: string,
        method: string,
        body?: object,
    ): Promise<T> {
        const url = `${this.baseUrl}${endpoint}`;
        const headers = {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.apiKey}`,
            "HTTP-Referer": typeof window !== "undefined"
                ? window.location.origin
                : "http://localhost:3000",
            "X-Title": "Landing Page Now",
        };

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(url, {
                method,
                headers,
                body: body ? JSON.stringify(body) : undefined,
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            // Extract relevant headers
            const rateLimit = response.headers.get("x-ratelimit-limit");
            const rateRemaining = response.headers.get("x-ratelimit-remaining");
            const xRequestId = response.headers.get("x-request-id");

            // Log headers for debugging
            if (rateLimit || rateRemaining || xRequestId) {
                console.log("[DEBUG] OpenRouter API headers:", {
                    rateLimit,
                    rateRemaining,
                    xRequestId,
                });
            }

            // Handle HTTP error responses
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));

                if (response.status === 401 || response.status === 403) {
                    throw new ApiKeyError(
                        errorData.error?.message ?? "Invalid API key",
                        response.status,
                    );
                }

                if (response.status === 429) {
                    throw new RateLimitError(
                        errorData.error?.message ?? "Rate limit exceeded",
                        response.status,
                    );
                }

                if (response.status === 400) {
                    throw new ValidationError(
                        errorData.error?.message ?? "Invalid request",
                        response.status,
                    );
                }

                throw new NetworkError(
                    errorData.error?.message ??
                        `Error ${response.status}: ${response.statusText}`,
                    response.status,
                );
            }

            return await response.json() as T;
        } catch (error) {
            clearTimeout(timeoutId);

            if (
                error instanceof ApiKeyError ||
                error instanceof RateLimitError ||
                error instanceof ValidationError ||
                error instanceof NetworkError
            ) {
                throw error;
            }

            if (error instanceof Error && error.name === "AbortError") {
                throw new NetworkError("Request timed out", 408);
            }

            throw handleNetworkError(error);
        }
    }

    /**
     * Creates a chat completion
     * @param request Chat completion request
     * @returns Chat completion response
     */
    async createChatCompletion(
        request: ChatCompletionRequest,
    ): Promise<ChatCompletionResponse> {
        // Sanitize message content
        const sanitizedMessages = request.messages.map((message) => ({
            ...message,
            content: sanitizeContent(message.content),
        }));

        return this.makeRequest<ChatCompletionResponse>(
            "/chat/completions",
            "POST",
            {
                ...request,
                messages: sanitizedMessages,
            },
        );
    }

    /**
     * Gets available models from OpenRouter
     * @returns List of available models
     */
    async getAvailableModels(): Promise<Model[]> {
        const response = await this.makeRequest<{ data: Model[] }>(
            "/models",
            "GET",
        );

        return response.data;
    }

    /**
     * Validates the API key by making a test request
     * @returns Boolean indicating if the API key is valid
     */
    async validateApiKey(): Promise<boolean> {
        try {
            await this.getAvailableModels();
            return true;
        } catch (error) {
            if (error instanceof ApiKeyError) {
                return false;
            }

            // If it's a different error (like network), we can't be sure
            // but since we're validating the API key, consider it potentially valid
            return true;
        }
    }
}
