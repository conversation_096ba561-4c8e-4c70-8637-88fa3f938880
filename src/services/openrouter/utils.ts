import { NetworkError } from "./types";

/**
 * Implements exponential backoff for retrying failed requests
 * @param fn Function to retry
 * @param maxRetries Maximum number of retries
 * @param initialDelay Initial delay in milliseconds
 * @returns Result of the function
 */
export async function retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    initialDelay: number = 500,
): Promise<T> {
    let retries = 0;
    let lastError: Error = new Error("Unknown error occurred");

    while (retries <= maxRetries) {
        try {
            return await fn();
        } catch (error) {
            lastError = error instanceof Error
                ? error
                : new Error(String(error));

            if (retries === maxRetries) {
                break;
            }

            const delay = initialDelay * Math.pow(2, retries);
            await new Promise((resolve) => setTimeout(resolve, delay));
            retries++;
        }
    }

    throw lastError;
}

/**
 * Validates API key format
 * @param apiKey API key to validate
 * @returns Boolean indicating if the key is valid
 */
export function isValidApiKey(apiKey: string): boolean {
    // Simple validation to check if API key has a reasonable format
    return typeof apiKey === "string" && apiKey.trim().length > 10;
}

/**
 * Handles network errors and provides useful error messages
 * @param error Error object
 * @returns NetworkError with appropriate message
 */
export function handleNetworkError(error: unknown): NetworkError {
    let message = "Unknown network error";
    let statusCode: number | undefined = undefined;

    if (error instanceof Error) {
        message = error.message;
    }

    if (typeof error === "object" && error !== null) {
        const errorObj = error as Record<string, unknown>;

        if (errorObj.status && typeof errorObj.status === "number") {
            statusCode = errorObj.status;
        }

        if (errorObj.message && typeof errorObj.message === "string") {
            message = errorObj.message;
        }
    }

    return new NetworkError(message, statusCode);
}

/**
 * Sanitizes user input before sending to API
 * @param content Text content to sanitize
 * @returns Sanitized content
 */
export function sanitizeContent(content: string): string {
    // Simple sanitization to remove potentially harmful patterns
    return content.trim();
}

/**
 * Generates a random request ID for tracking
 * @returns Random ID string
 */
export function generateRequestId(): string {
    return `req_${Math.random().toString(36).substring(2, 12)}`;
}
