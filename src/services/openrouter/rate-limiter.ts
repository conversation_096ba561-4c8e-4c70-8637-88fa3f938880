export class TokenBucket {
    private tokens: number;
    private lastRefill: number;
    private dailyUsage: number;
    private lastDayReset: number;

    constructor(
        private readonly capacity: number,
        private readonly refillRate: number, // tokens per second
        private readonly refillInterval: number = 1000, // ms
        private readonly dailyLimit: number = 100, // daily page generation limit
    ) {
        this.tokens = capacity;
        this.lastRefill = Date.now();
        this.dailyUsage = 0;
        this.lastDayReset = this.getStartOfDay();
    }

    private getStartOfDay(): number {
        const now = new Date();
        return new Date(now.getFullYear(), now.getMonth(), now.getDate())
            .getTime();
    }

    private resetDailyUsageIfNeeded(): void {
        const currentDayStart = this.getStartOfDay();
        if (currentDayStart > this.lastDayReset) {
            this.dailyUsage = 0;
            this.lastDayReset = currentDayStart;
        }
    }

    private refill(): void {
        const now = Date.now();
        const timePassed = now - this.lastRefill;
        const newTokens = (timePassed / this.refillInterval) * this.refillRate;
        this.tokens = Math.min(this.capacity, this.tokens + newTokens);
        this.lastRefill = now;
        this.resetDailyUsageIfNeeded();
    }

    public async consume(tokens: number = 1): Promise<boolean> {
        this.refill();
        this.resetDailyUsageIfNeeded();

        if (this.tokens < tokens || this.dailyUsage >= this.dailyLimit) {
            return false;
        }

        this.tokens -= tokens;
        this.dailyUsage += 1; // Increment daily usage
        return true;
    }

    public async waitForTokens(tokens: number = 1): Promise<void> {
        while (!(await this.consume(tokens))) {
            await new Promise((resolve) =>
                setTimeout(resolve, this.refillInterval)
            );
        }
    }

    public async getUsageStats(): Promise<{
        currentTokens: number;
        maxTokens: number;
        dailyUsage: number;
        dailyLimit: number;
        remainingToday: number;
        nextResetTime: string;
    }> {
        this.refill();
        this.resetDailyUsageIfNeeded();

        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        return {
            currentTokens: Math.floor(this.tokens),
            maxTokens: this.capacity,
            dailyUsage: this.dailyUsage,
            dailyLimit: this.dailyLimit,
            remainingToday: Math.max(0, this.dailyLimit - this.dailyUsage),
            nextResetTime: tomorrow.toISOString(),
        };
    }
}
