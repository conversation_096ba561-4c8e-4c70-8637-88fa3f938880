import { z } from "zod";

/**
 * OpenRouter API types
 */

/**
 * OpenRouter configuration options
 */
export interface OpenRouterConfig {
    /** API key for OpenRouter */
    apiKey: string;
    /** Base URL for the OpenRouter API */
    baseUrl?: string;
    /** Default model to use if none specified */
    defaultModel?: string;
    /** Maximum number of retries for failed requests */
    maxRetries?: number;
    /** Timeout in milliseconds for requests */
    timeout?: number;
    reasoning?: {
        exclude?: boolean;
    };
}

/**
 * Chat message object
 */
export interface ChatMessage {
    /** Role of the message sender */
    role: "system" | "user" | "assistant";
    /** Content of the message */
    content: string;
}

/**
 * Schema for JSON response format
 */
export interface JsonSchema {
    name: string;
    strict: boolean;
    schema: Record<string, unknown>;
}

/**
 * Chat completion request parameters
 */
export interface ChatCompletionRequest {
    /** Array of messages in the conversation */
    messages: ChatMessage[];
    /** Model to use for completion */
    model?: string;
    /** Temperature for randomness (0-1) */
    temperature?: number;
    /** Maximum tokens to generate */
    max_tokens?: number;
    /** Format for the response */
    response_format?: {
        type: "json_schema";
        json_schema: JsonSchema;
    };
    /** Usage information options */
    usage?: {
        /** Whether to include detailed usage information in the response */
        include_usage: boolean;
    };
    /** Reasoning configuration for models that support thinking tokens */
    reasoning?: {
        /** Whether to exclude reasoning tokens from the response */
        exclude?: boolean;
        /** Effort level for reasoning (OpenAI-style) */
        effort?: "high" | "medium" | "low";
        /** Maximum tokens to use for reasoning (Anthropic-style) */
        max_tokens?: number;
    };
}

/**
 * Chat completion response from OpenRouter
 */
export interface ChatCompletionResponse {
    id: string;
    model: string;
    object: string;
    created: number;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
        // Cost returned directly from the API - primary source of truth
        cost?: number;
        // Currency code - defaults to USD
        currency?: string;
        // Detailed token information
        prompt_tokens_details?: {
            cached_tokens?: number;
        };
        completion_tokens_details?: {
            reasoning_tokens?: number;
        };
        // These fields might be included in some responses for backwards compatibility
        route?: string;
        input_cost?: number;
        output_cost?: number;
        total_cost?: number;
    };
}

/**
 * Model information
 */
export interface Model {
    id: string;
    name: string;
    description?: string;
    context_length: number;
    pricing?: {
        prompt: number;
        completion: number;
    };
}

/**
 * OpenRouter error types
 */
export enum ErrorCode {
    API_KEY_ERROR = "api_key_error",
    RATE_LIMIT_ERROR = "rate_limit_error",
    VALIDATION_ERROR = "validation_error",
    NETWORK_ERROR = "network_error",
    UNKNOWN_ERROR = "unknown_error",
}

/**
 * Base OpenRouter error class
 */
export class OpenRouterError extends Error {
    constructor(
        message: string,
        public readonly code: ErrorCode,
        public readonly statusCode?: number,
    ) {
        super(message);
        this.name = "OpenRouterError";
    }
}

/**
 * API key error
 */
export class ApiKeyError extends OpenRouterError {
    constructor(message: string, statusCode?: number) {
        super(message, ErrorCode.API_KEY_ERROR, statusCode);
        this.name = "ApiKeyError";
    }
}

/**
 * Rate limit error
 */
export class RateLimitError extends OpenRouterError {
    constructor(message: string, statusCode?: number) {
        super(message, ErrorCode.RATE_LIMIT_ERROR, statusCode);
        this.name = "RateLimitError";
    }
}

/**
 * Validation error
 */
export class ValidationError extends OpenRouterError {
    constructor(message: string, statusCode?: number) {
        super(message, ErrorCode.VALIDATION_ERROR, statusCode);
        this.name = "ValidationError";
    }
}

/**
 * Network error
 */
export class NetworkError extends OpenRouterError {
    constructor(message: string, statusCode?: number) {
        super(message, ErrorCode.NETWORK_ERROR, statusCode);
        this.name = "NetworkError";
    }
}

// Zod schemas for validation
export const chatMessageSchema = z.object({
    role: z.enum(["system", "user", "assistant"]),
    content: z.string(),
});

export const chatCompletionResponseSchema = z.object({
    id: z.string(),
    choices: z.array(z.object({
        message: chatMessageSchema,
        finish_reason: z.string(),
    })),
    model: z.string(),
    created: z.number(),
    usage: z.object({
        prompt_tokens: z.number(),
        completion_tokens: z.number(),
        total_tokens: z.number(),
    }),
});
