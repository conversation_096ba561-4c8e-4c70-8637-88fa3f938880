import { z } from "zod";
import {
    ChatCompletionRequest,
    ChatCompletionResponse,
    Model,
    OpenRouterConfig,
} from "./types";
import {
    NetworkError,
    OpenRouterError,
    RateLimitError,
    ValidationError,
} from "./errors";
import { chatCompletionResponseSchema } from "./schemas";
import { OpenRouterClient } from "./client";
import { isValidApiKey } from "./utils";

/**
 * OpenRouterService - Main service for interacting with OpenRouter
 */
export class OpenRouterService {
    private client: OpenRouterClient;
    private readonly config: OpenRouterConfig;
    private readonly isInitialized: boolean = false;
    private lastError: Error | null = null;

    /**
     * Creates a new OpenRouter service
     * @param config Configuration options
     */
    constructor(config: OpenRouterConfig) {
        if (!isValidApiKey(config.apiKey)) {
            throw new Error("Invalid API key format");
        }

        this.config = {
            apiKey: config.apiKey,
            baseUrl: config.baseUrl ?? "https://openrouter.ai/api/v1",
            defaultModel: config.defaultModel ?? "anthropic/claude-3-haiku",
            maxRetries: config.maxRetries ?? 3,
            timeout: config.timeout ?? 60000,
            reasoning: config.reasoning ?? { exclude: true },
        };

        this.client = new OpenRouterClient(this.config);
        // Remove rate limiter and request queue to simplify and avoid hanging issues
        this.isInitialized = true;
    }

    private async handleRequest<T>(
        endpoint: string,
        options: RequestInit = {},
    ): Promise<T> {
        if (!this.isInitialized) {
            throw new OpenRouterError(
                "Service not initialized",
                "NOT_INITIALIZED",
            );
        }

        const url = `${this.config.baseUrl}${endpoint}`;
        const headers = {
            "Authorization": `Bearer ${this.config.apiKey}`,
            "Content-Type": "application/json",
            ...options.headers,
        };

        try {
            const response = await fetch(url, {
                ...options,
                headers,
                signal: AbortSignal.timeout(this.config.timeout ?? 60000),
            });

            if (response.status === 429) {
                throw new RateLimitError("Rate limit exceeded");
            }

            if (!response.ok) {
                throw new NetworkError(
                    `HTTP error! status: ${response.status}`,
                );
            }

            const data = await response.json();
            return data as T;
        } catch (error) {
            this.lastError = error as Error;
            throw error;
        }
    }

    private parseAndYieldChunks = async function* (
        buffer: string,
        chatCompletionResponseSchema: z.ZodSchema<ChatCompletionResponse>,
        validateResponse: (
            response: unknown,
            schema: z.ZodSchema<ChatCompletionResponse>,
        ) => ChatCompletionResponse,
    ): AsyncGenerator<ChatCompletionResponse, void, void> {
        const lines = buffer.split("\n");
        lines.pop(); // Remove last buffer, not needed for return
        for (const line of lines) {
            if (line.startsWith("data: ")) {
                const data = line.slice(6);
                if (data === "[DONE]") break;
                try {
                    const parsed = JSON.parse(data);
                    const validated = validateResponse(
                        parsed,
                        chatCompletionResponseSchema,
                    );
                    yield validated;
                } catch (error) {
                    // Log error for debugging but continue processing
                    console.warn(
                        "Failed to parse streaming response chunk:",
                        error,
                    );
                    continue;
                }
            }
        }
        return;
    };

    private validateStreamingResponse(response: Response) {
        if (!response.ok) {
            if (response.status === 429) {
                throw new RateLimitError("Rate limit exceeded");
            }
            throw new NetworkError(`HTTP error! status: ${response.status}`);
        }
        if (!response.body) {
            throw new NetworkError("Response body is null");
        }
    }

    private async *handleStreamingRequest(
        endpoint: string,
        options: RequestInit = {},
    ): AsyncGenerator<ChatCompletionResponse> {
        if (!this.isInitialized) {
            throw new OpenRouterError(
                "Service not initialized",
                "NOT_INITIALIZED",
            );
        }

        const url = `${this.config.baseUrl}${endpoint}`;
        const headers = {
            "Authorization": `Bearer ${this.config.apiKey}`,
            "Content-Type": "application/json",
            "Accept": "text/event-stream",
            ...options.headers,
        };

        const response = await fetch(url, {
            ...options,
            headers,
            signal: AbortSignal.timeout(this.config.timeout ?? 60000),
        });

        this.validateStreamingResponse(response);
        const reader = response.body!.getReader();
        const decoder = new TextDecoder();
        let buffer = "";
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                buffer += decoder.decode(value, { stream: true });
                yield* this.parseAndYieldChunks(
                    buffer,
                    chatCompletionResponseSchema,
                    this.validateResponse.bind(this),
                );
                buffer = "";
            }
        } finally {
            reader.releaseLock();
        }
    }

    private validateResponse<T>(response: unknown, schema: z.ZodSchema<T>): T {
        try {
            return schema.parse(response);
        } catch (error) {
            throw new ValidationError(
                "Invalid response format",
                error as Error,
            );
        }
    }

    /**
     * Creates a chat completion
     * @param request Chat completion request
     * @returns Promise with chat completion response
     */
    async createChatCompletion(
        request: ChatCompletionRequest,
        stream = false,
    ): Promise<
        ChatCompletionResponse | AsyncGenerator<ChatCompletionResponse>
    > {
        this.ensureInitialized();

        // Use default model if not specified
        const model = request.model ?? this.config.defaultModel;
        if (!model) {
            throw new Error(
                "No model specified and no default model configured",
            );
        }

        // Apply reasoning configuration from service config if not specified in request
        const requestWithReasoning = {
            ...request,
            model,
            reasoning: request.reasoning ?? this.config.reasoning,
        };

        if (stream) {
            return this.handleStreamingRequest("/chat", {
                method: "POST",
                body: JSON.stringify({ ...requestWithReasoning, stream: true }),
            });
        }

        try {
            // Use client directly without additional retry wrapper to avoid conflicts
            const response = await this.client.createChatCompletion(
                requestWithReasoning,
            );

            // Log usage information if available
            if (request.usage?.include_usage && response.usage) {
                console.log(
                    `[DEBUG] Usage for model ${model}:`,
                    response.usage,
                );
            }

            return response;
        } catch (error) {
            this.lastError = error instanceof Error
                ? error
                : new Error(String(error));
            throw error;
        }
    }

    /**
     * Gets available models from OpenRouter
     * @returns Promise with list of available models
     */
    async getAvailableModels(): Promise<Model[]> {
        this.ensureInitialized();

        try {
            return await this.client.getAvailableModels();
        } catch (error) {
            this.lastError = error instanceof Error
                ? error
                : new Error(String(error));
            throw error;
        }
    }

    /**
     * Validates the API key
     * @returns Promise with boolean indicating if key is valid
     */
    async validateApiKey(): Promise<boolean> {
        this.ensureInitialized();

        try {
            return await this.client.validateApiKey();
        } catch (error) {
            this.lastError = error instanceof Error
                ? error
                : new Error(String(error));
            return false;
        }
    }

    /**
     * Updates the service configuration
     * @param config New configuration options (partial)
     */
    setConfig(config: Partial<OpenRouterConfig>): void {
        // Update config only with provided values
        this.config.apiKey = config.apiKey ?? this.config.apiKey;
        this.config.baseUrl = config.baseUrl ?? this.config.baseUrl;
        this.config.defaultModel = config.defaultModel ??
            this.config.defaultModel;
        this.config.maxRetries = config.maxRetries ?? this.config.maxRetries;
        this.config.timeout = config.timeout ?? this.config.timeout;
        this.config.reasoning = config.reasoning ?? this.config.reasoning;

        // Reinitialize client with new config
        this.client = new OpenRouterClient(this.config);
    }

    /**
     * Gets the current configuration
     * @returns Current configuration
     */
    getConfig(): Readonly<OpenRouterConfig> {
        return { ...this.config };
    }

    /**
     * Checks if the service is initialized
     * @returns Boolean indicating if initialized
     */
    getIsInitialized(): boolean {
        return this.isInitialized;
    }

    /**
     * Gets the last error
     * @returns Last error or null
     */
    getLastError(): Error | null {
        return this.lastError;
    }

    /**
     * Ensures the service is initialized
     * @throws Error if not initialized
     */
    private ensureInitialized(): void {
        if (!this.isInitialized) {
            throw new Error("OpenRouterService not initialized");
        }
    }

    /**
     * Fetches detailed information about a specific generation by ID
     * @param generationId The OpenRouter generation ID
     * @returns Promise with detailed generation information
     */
    public async getGenerationDetails(generationId: string): Promise<{
        id: string;
        total_cost: number;
        tokens_prompt: number;
        tokens_completion: number;
        model: string;
        provider_name?: string;
        usage?: number;
        cache_discount?: number;
        // Additional fields may be present but these are the ones we use
        [key: string]: unknown;
    }> {
        this.ensureInitialized();

        const maxRetries = 3;
        const retryDelay = 3000; // 3 seconds
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                const data = await this.handleRequest<
                    { data: Record<string, unknown> }
                >(
                    `/generation?id=${encodeURIComponent(generationId)}`,
                    { method: "GET" },
                );

                return data.data as {
                    id: string;
                    total_cost: number;
                    tokens_prompt: number;
                    tokens_completion: number;
                    model: string;
                    provider_name?: string;
                    usage?: number;
                    cache_discount?: number;
                    [key: string]: unknown;
                };
            } catch (error) {
                if (
                    error instanceof NetworkError && retryCount < maxRetries - 1
                ) {
                    retryCount++;
                    console.log(
                        `[INFO] Generation data not yet available for ${generationId}, retrying in ${retryDelay}ms (attempt ${retryCount}/${maxRetries})`,
                    );

                    await new Promise((resolve) =>
                        setTimeout(resolve, retryDelay)
                    );
                    continue;
                }

                console.error(
                    `[ERROR] Failed to fetch generation details for ${generationId}:`,
                    error,
                );
                throw error;
            }
        }

        throw new Error(
            `Failed to fetch generation details for ${generationId} after ${maxRetries} attempts`,
        );
    }

    /**
     * Calculates the cost of a request based on token usage and model
     * This method is deprecated and only maintained for backward compatibility.
     * The accurate cost is returned directly in the API response's usage field.
     */

    public calculateCost(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _model: string,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _promptTokens: number,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _completionTokens: number,
    ): {
        promptCost: number;
        completionCost: number;
        totalCost: number;
        currency: string;
    } {
        console.warn(
            "[WARNING] calculateCost is deprecated - use the cost directly from the API response",
        );

        // Return empty cost structure - API response should be used instead
        return {
            promptCost: 0,
            completionCost: 0,
            totalCost: 0,
            currency: "USD",
        };
    }
}

/**
 * Creates a new OpenRouter service instance
 * @param config Configuration options
 * @returns OpenRouterService instance
 */
export function createOpenRouterService(
    config: OpenRouterConfig,
): OpenRouterService {
    return new OpenRouterService(config);
}
