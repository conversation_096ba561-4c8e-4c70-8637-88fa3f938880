interface QueuedRequest<T> {
    execute: () => Promise<T>;
    priority: number;
    timestamp: number;
    resolve: (value: T) => void;
    reject: (error: Error) => void;
}

export class RequestQueue {
    private queue: QueuedRequest<unknown>[] = [];
    private isProcessing = false;
    private maxConcurrent: number;
    private activeRequests = 0;

    constructor(maxConcurrent = 3) {
        this.maxConcurrent = maxConcurrent;
    }

    public async enqueue<T>(
        execute: () => Promise<T>,
        priority = 0,
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const request: QueuedRequest<T> = {
                execute,
                priority,
                timestamp: Date.now(),
                resolve,
                reject,
            };

            this.addToQueue(request);
            this.processQueue();
        });
    }

    private addToQueue<T>(request: QueuedRequest<T>): void {
        // Insert request maintaining priority order (higher priority first)
        const index = this.queue.findIndex(
            (item) =>
                item.priority < request.priority ||
                (item.priority === request.priority &&
                    item.timestamp > request.timestamp),
        );

        if (index === -1) {
            this.queue.push(request as QueuedRequest<unknown>);
        } else {
            this.queue.splice(index, 0, request as QueuedRequest<unknown>);
        }
    }

    private async processQueue(): Promise<void> {
        if (
            this.isProcessing || this.queue.length === 0 ||
            this.activeRequests >= this.maxConcurrent
        ) {
            return;
        }

        this.isProcessing = true;

        try {
            while (
                this.queue.length > 0 &&
                this.activeRequests < this.maxConcurrent
            ) {
                const request = this.queue.shift()!;
                this.activeRequests++;

                // Execute request in the background
                this.executeRequest(request).finally(() => {
                    this.activeRequests--;
                    this.processQueue();
                });
            }
        } finally {
            this.isProcessing = false;
        }
    }

    private async executeRequest(
        request: QueuedRequest<unknown>,
    ): Promise<void> {
        try {
            const result = await request.execute();
            request.resolve(result);
        } catch (error) {
            request.reject(error as Error);
        }
    }

    public get length(): number {
        return this.queue.length;
    }

    public get active(): number {
        return this.activeRequests;
    }
}
