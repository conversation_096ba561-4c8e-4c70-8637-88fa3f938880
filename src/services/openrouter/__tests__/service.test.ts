import { createOpenRouterService, OpenRouterService } from "../service";
import { OpenRouterClient } from "../client";
import { ChatCompletionRequest, ChatCompletionResponse } from "../types";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock OpenRouterClient
vi.mock("../client");

describe("OpenRouterService", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    const mockConfig = {
        apiKey: "test-api-key-12345",
        baseUrl: "https://test-api.com",
        defaultModel: "test-model",
        maxRetries: 2,
        timeout: 5000,
    };

    describe("constructor", () => {
        it("should create an instance with valid config", () => {
            const service = new OpenRouterService(mockConfig);
            expect(service).toBeInstanceOf(OpenRouterService);
            expect(service.getIsInitialized()).toBe(true);
        });

        it("should throw an error with invalid API key", () => {
            const invalidConfig = { ...mockConfig, apiKey: "" };
            expect(() => new OpenRouterService(invalidConfig)).toThrow();
        });
    });

    describe("createChatCompletion", () => {
        it("should call client with correct parameters", async () => {
            // Setup mock response
            const mockResponse: ChatCompletionResponse = {
                id: "test-id",
                model: "test-model",
                object: "chat.completion",
                created: Date.now(),
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: "Test response",
                        },
                        finish_reason: "stop",
                    },
                ],
                usage: {
                    prompt_tokens: 10,
                    completion_tokens: 5,
                    total_tokens: 15,
                },
            };

            // Setup mock client implementation
            (OpenRouterClient.prototype
                .createChatCompletion as import("vitest").Mock)
                .mockResolvedValue(
                    mockResponse,
                );

            const service = createOpenRouterService(mockConfig);
            const request: ChatCompletionRequest = {
                messages: [
                    { role: "system", content: "You are a helpful assistant." },
                    { role: "user", content: "Hello!" },
                ],
            };

            const result = await service.createChatCompletion(request);

            // Verify client was called with correct parameters
            expect(OpenRouterClient.prototype.createChatCompletion)
                .toHaveBeenCalledWith({
                    ...request,
                    model: mockConfig.defaultModel,
                    reasoning: { exclude: true },
                });

            // Verify result is correct
            expect(result).toEqual(mockResponse);
        });

        it("should use provided model instead of default", async () => {
            // Setup mock response
            const mockResponse: ChatCompletionResponse = {
                id: "test-id",
                model: "custom-model",
                object: "chat.completion",
                created: Date.now(),
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: "Test response",
                        },
                        finish_reason: "stop",
                    },
                ],
                usage: {
                    prompt_tokens: 10,
                    completion_tokens: 5,
                    total_tokens: 15,
                },
            };

            // Setup mock client implementation
            (OpenRouterClient.prototype
                .createChatCompletion as import("vitest").Mock)
                .mockResolvedValue(
                    mockResponse,
                );

            const service = createOpenRouterService(mockConfig);
            const request: ChatCompletionRequest = {
                messages: [
                    { role: "system", content: "You are a helpful assistant." },
                    { role: "user", content: "Hello!" },
                ],
                model: "custom-model",
            };

            await service.createChatCompletion(request);

            // Verify client was called with correct parameters
            expect(OpenRouterClient.prototype.createChatCompletion)
                .toHaveBeenCalledWith({
                    ...request,
                    model: "custom-model",
                    reasoning: { exclude: true },
                });
        });
    });
});
