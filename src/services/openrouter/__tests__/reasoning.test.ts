import { createOpenRouterService } from "../service";
import { OpenRouterClient } from "../client";
import { ChatCompletionRequest, ChatCompletionResponse } from "../types";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock OpenRouterClient
vi.mock("../client");

describe("OpenRouterService - Reasoning Tokens", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    const mockConfig = {
        apiKey: "test-api-key-12345",
        baseUrl: "https://test-api.com",
        defaultModel: "google/gemini-2.5-flash-preview:thinking",
        maxRetries: 2,
        timeout: 5000,
    };

    describe("reasoning token exclusion", () => {
        it("should exclude reasoning tokens by default", async () => {
            const mockResponse: ChatCompletionResponse = {
                id: "test-id",
                model: "google/gemini-2.5-flash-preview:thinking",
                object: "chat.completion",
                created: Date.now(),
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: "Test response without reasoning",
                        },
                        finish_reason: "stop",
                    },
                ],
                usage: {
                    prompt_tokens: 10,
                    completion_tokens: 5,
                    total_tokens: 15,
                },
            };

            (OpenRouterClient.prototype
                .createChatCompletion as import("vitest").Mock)
                .mockResolvedValue(mockResponse);

            const service = createOpenRouterService(mockConfig);
            const request: ChatCompletionRequest = {
                messages: [
                    { role: "user", content: "Explain quantum computing" },
                ],
            };

            await service.createChatCompletion(request);

            // Verify that reasoning exclusion is enabled by default
            expect(OpenRouterClient.prototype.createChatCompletion)
                .toHaveBeenCalledWith({
                    ...request,
                    model: mockConfig.defaultModel,
                    reasoning: { exclude: true },
                });
        });

        it("should allow overriding reasoning configuration in request", async () => {
            const mockResponse: ChatCompletionResponse = {
                id: "test-id",
                model: "google/gemini-2.5-flash-preview:thinking",
                object: "chat.completion",
                created: Date.now(),
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: "Test response with reasoning",
                        },
                        finish_reason: "stop",
                    },
                ],
                usage: {
                    prompt_tokens: 10,
                    completion_tokens: 5,
                    total_tokens: 15,
                },
            };

            (OpenRouterClient.prototype
                .createChatCompletion as import("vitest").Mock)
                .mockResolvedValue(mockResponse);

            const service = createOpenRouterService(mockConfig);
            const request: ChatCompletionRequest = {
                messages: [
                    { role: "user", content: "Explain quantum computing" },
                ],
                reasoning: { exclude: false, effort: "high" },
            };

            await service.createChatCompletion(request);

            // Verify that request-level reasoning config overrides service config
            expect(OpenRouterClient.prototype.createChatCompletion)
                .toHaveBeenCalledWith({
                    ...request,
                    model: mockConfig.defaultModel,
                    reasoning: { exclude: false, effort: "high" },
                });
        });

        it("should work with custom reasoning configuration in service config", async () => {
            const customConfig = {
                ...mockConfig,
                reasoning: { exclude: false, max_tokens: 2000 },
            };

            const mockResponse: ChatCompletionResponse = {
                id: "test-id",
                model: "google/gemini-2.5-flash-preview:thinking",
                object: "chat.completion",
                created: Date.now(),
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: "Test response with reasoning tokens",
                        },
                        finish_reason: "stop",
                    },
                ],
                usage: {
                    prompt_tokens: 10,
                    completion_tokens: 5,
                    total_tokens: 15,
                },
            };

            (OpenRouterClient.prototype
                .createChatCompletion as import("vitest").Mock)
                .mockResolvedValue(mockResponse);

            const service = createOpenRouterService(customConfig);
            const request: ChatCompletionRequest = {
                messages: [
                    {
                        role: "user",
                        content: "Solve this complex math problem",
                    },
                ],
            };

            await service.createChatCompletion(request);

            // Verify that custom service config is used
            expect(OpenRouterClient.prototype.createChatCompletion)
                .toHaveBeenCalledWith({
                    ...request,
                    model: customConfig.defaultModel,
                    reasoning: { exclude: false, max_tokens: 2000 },
                });
        });
    });
});
