/**
 * OpenRouter Service - Main exports
 */

// Re-export main service class and factory function
export { createOpenRouterService, OpenRouterService } from "./service";

// Re-export client for advanced usage
export { OpenRouterClient } from "./client";

// Re-export types
export type {
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatMessage,
    JsonSchema,
    Model,
    OpenRouterConfig,
} from "./types";

// Re-export error classes and enums
export {
    ApiKeyError,
    ErrorCode,
    NetworkError,
    OpenRouterError,
    RateLimitError,
    ValidationError,
} from "./types";

// Re-export utility functions
export {
    generateRequestId,
    isValidApiKey,
    retryWithBackoff,
    sanitizeContent,
} from "./utils";
