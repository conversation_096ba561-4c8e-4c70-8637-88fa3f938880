import { beforeEach, describe, expect, it, vi } from "vitest";
import { ImageValidatorService } from "../service";

// Mock Unsplash service
vi.mock("../../unsplash", () => ({
    unsplash: {
        getRandomImageUrl: vi.fn().mockResolvedValue(
            "https://unsplash.com/fallback.jpg",
        ),
        extractKeywords: vi.fn().mockReturnValue(["business"]),
    },
}));

describe("ImageValidatorService", () => {
    let service: ImageValidatorService;

    beforeEach(() => {
        service = new ImageValidatorService();
        vi.clearAllMocks();
    });

    describe("validateSingleImage", () => {
        it("should return valid result for successful response", async () => {
            const result = await service.validateSingleImage(
                "https://example.com/image.jpg",
            );

            expect(result).toEqual({
                url: "https://example.com/image.jpg",
                isValid: true,
                httpStatus: 200,
            });
        });

        it("should return invalid result with replacement URL for failed response", async () => {
            const result = await service.validateSingleImage(
                "https://invalid.com/image.jpg",
            );

            expect(result.isValid).toBe(false);
            expect(result.httpStatus).toBe(404);
            expect(result.error).toBe("HTTP 404");
        });
    });

    describe("validateAndFixHtml", () => {
        it("should replace invalid images with Unsplash fallback URLs", async () => {
            const html = `
        <div>
          <img src="https://valid.com/image.jpg" alt="valid" />
          <img src="https://invalid.com/image.jpg" alt="invalid" />
        </div>
      `;

            const result = await service.validateAndFixHtml(html);

            expect(result.summary.totalImages).toBe(2);
            expect(result.summary.validImages).toBe(1);
            expect(result.summary.invalidImages).toBe(1);
            expect(result.summary.replacedImages).toBe(1);
            expect(result.html).not.toContain("https://invalid.com/image.jpg");
            expect(result.html).toContain("https://valid.com/image.jpg");
            expect(result.html).toContain("https://unsplash.com/fallback.jpg");
        });

        it("should handle HTML with no images", async () => {
            const html = "<div><p>No images</p></div>";

            const result = await service.validateAndFixHtml(html);

            expect(result.summary.totalImages).toBe(0);
            expect(result.summary.validImages).toBe(0);
            expect(result.summary.invalidImages).toBe(0);
            expect(result.summary.replacedImages).toBe(0);
            expect(result.html).toBe(html);
        });
    });

    describe("getValidationStats", () => {
        it("should return validation statistics without modifying HTML", async () => {
            const html = `
        <img src="https://valid.com/image.jpg" alt="valid" />
        <img src="https://invalid.com/image.jpg" alt="invalid" />
      `;

            const stats = await service.getValidationStats(html);

            expect(stats.totalImages).toBe(2);
            expect(stats.validImages).toBe(1);
            expect(stats.invalidImages).toBe(1);
            expect(stats.replacedImages).toBe(0); // Stats mode doesn't replace
        });
    });
});
