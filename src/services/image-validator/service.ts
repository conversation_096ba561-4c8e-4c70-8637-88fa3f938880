import {
    ImageValidationOptions,
    ImageValidationResult,
    ImageValidationSummary,
} from "./types";
import { unsplash } from "../unsplash";

export class ImageValidatorService {
    private readonly defaultOptions: Required<ImageValidationOptions> = {
        timeout: 10000, // 10 seconds
        retries: 2,
    };

    constructor(private options: ImageValidationOptions = {}) {
        this.options = { ...this.defaultOptions, ...options };
    }

    /**
     * Validates images in HTML content and replaces invalid ones
     */
    async validateAndFixHtml(html: string): Promise<{
        html: string;
        summary: ImageValidationSummary;
    }> {
        const imageUrls = this.extractImageUrls(html);
        const validationResults = await this.validateImages(imageUrls);

        let updatedHtml = html;
        let replacedCount = 0;

        for (const result of validationResults) {
            if (!result.isValid) {
                const replacementUrl = await this.generateFallbackUrl(
                    result.url,
                    html,
                );
                updatedHtml = updatedHtml.replace(result.url, replacementUrl);
                replacedCount++;
            }
        }

        const summary: ImageValidationSummary = {
            totalImages: imageUrls.length,
            validImages: validationResults.filter((r) => r.isValid).length,
            invalidImages: validationResults.filter((r) => !r.isValid).length,
            results: validationResults.map((r) => ({
                ...r,
                replacementUrl: r.isValid
                    ? undefined
                    : updatedHtml.includes(r.url)
                    ? undefined
                    : r.url,
            })),
            replacedImages: replacedCount,
        };

        return { html: updatedHtml, summary };
    }

    /**
     * Validates multiple images concurrently
     */
    async validateImages(urls: string[]): Promise<ImageValidationResult[]> {
        const promises = urls.map((url) => this.validateSingleImage(url));
        return Promise.all(promises);
    }

    /**
     * Validates a single image URL
     */
    async validateSingleImage(url: string): Promise<ImageValidationResult> {
        let lastError: string | undefined;
        let lastStatus: number | undefined;

        for (let attempt = 0; attempt <= this.options.retries!; attempt++) {
            try {
                const response = await this.fetchWithTimeout(url);

                if (response.ok) {
                    return {
                        url,
                        isValid: true,
                        httpStatus: response.status,
                    };
                } else {
                    lastStatus = response.status;
                    lastError = `HTTP ${response.status}`;
                }
            } catch (error) {
                lastError = error instanceof Error
                    ? error.message
                    : "Unknown error";
            }
        }

        // Generate fallback URL for invalid images
        const replacementUrl = await this.generateFallbackUrl(url);

        return {
            url,
            isValid: false,
            httpStatus: lastStatus,
            error: lastError,
            replacementUrl,
        };
    }

    /**
     * Extracts image URLs from HTML content
     */
    private extractImageUrls(html: string): string[] {
        const imageRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
        const urls: string[] = [];
        let match;

        while ((match = imageRegex.exec(html)) !== null) {
            const url = match[1];
            if (url && this.isExternalImage(url)) {
                urls.push(url);
            }
        }

        return [...new Set(urls)]; // Remove duplicates
    }

    /**
     * Checks if image is external (not local/data URL)
     */
    private isExternalImage(url: string): boolean {
        return url.startsWith("http://") || url.startsWith("https://");
    }

    /**
     * Fetch with timeout support
     */
    private async fetchWithTimeout(url: string): Promise<Response> {
        const controller = new AbortController();
        const timeoutId = setTimeout(
            () => controller.abort(),
            this.options.timeout,
        );

        try {
            const response = await fetch(url, {
                method: "HEAD", // Use HEAD to avoid downloading full image
                signal: controller.signal,
                headers: {
                    "User-Agent":
                        "Mozilla/5.0 (compatible; Landing Page Now Image Validator)",
                },
            });

            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * Generates fallback URL based on original URL or content context
     */
    private async generateFallbackUrl(
        originalUrl: string,
        contentContext?: string,
    ): Promise<string> {
        try {
            // Try to extract keywords from original URL for better replacement
            const extractedKeywords = this.extractKeywordsFromUrl(originalUrl);

            if (extractedKeywords.length > 0) {
                return await unsplash.getRandomImageUrl(extractedKeywords[0]);
            }

            // If we have content context, try to extract keywords from it
            if (contentContext) {
                const contentKeywords = unsplash.extractKeywords(
                    contentContext,
                );
                if (contentKeywords.length > 0) {
                    return await unsplash.getRandomImageUrl(contentKeywords[0]);
                }
            }

            return await unsplash.getRandomImageUrl("random");
        } catch {
            // If Unsplash API fails, return a default image
            return "https://placehold.co/800x600/2563eb/ffffff?text=Image+Placeholder";
        }
    }

    /**
     * Extracts potential keywords from image URL
     */
    private extractKeywordsFromUrl(url: string): string[] {
        const keywords: string[] = [];

        // Extract from Unsplash URLs
        const unsplashMatch = url.match(/unsplash\.com.*[&?]s=([^&]+)/);
        if (unsplashMatch) {
            keywords.push(unsplashMatch[1]);
        }

        // Extract from query parameters
        const queryMatch = url.match(/[&?](?:q|query|search|keyword)=([^&]+)/i);
        if (queryMatch) {
            keywords.push(decodeURIComponent(queryMatch[1]));
        }

        return keywords;
    }

    /**
     * Get validation statistics for monitoring
     */
    async getValidationStats(html: string): Promise<ImageValidationSummary> {
        const imageUrls = this.extractImageUrls(html);
        const validationResults = await this.validateImages(imageUrls);

        return {
            totalImages: imageUrls.length,
            validImages: validationResults.filter((r) => r.isValid).length,
            invalidImages: validationResults.filter((r) => !r.isValid).length,
            results: validationResults,
            replacedImages: 0, // Not replaced in stats mode
        };
    }
}

/**
 * Default instance for easy import
 */
export const imageValidator = new ImageValidatorService();
