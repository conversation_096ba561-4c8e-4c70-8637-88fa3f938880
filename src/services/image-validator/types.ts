export interface ImageValidationResult {
    url: string;
    isValid: boolean;
    httpStatus?: number;
    error?: string;
    replacementUrl?: string;
}

export interface ImageValidationSummary {
    totalImages: number;
    validImages: number;
    invalidImages: number;
    results: ImageValidationResult[];
    replacedImages: number;
}

export interface ImageValidationOptions {
    timeout?: number;
    retries?: number;
}

export class ImageValidationError extends Error {
    constructor(
        message: string,
        public readonly url: string,
        public readonly status?: number,
    ) {
        super(message);
        this.name = "ImageValidationError";
    }
}
