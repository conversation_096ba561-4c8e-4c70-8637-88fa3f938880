import { supabaseAdmin } from "@/db/supabase";

export interface UserLimits {
    dailyPageLimit: number;
    dailyUsage: number;
    remainingToday: number;
    nextResetTime: string;
}

export class UserLimitsService {
    private static instance: UserLimitsService;

    static getInstance(): UserLimitsService {
        if (!UserLimitsService.instance) {
            UserLimitsService.instance = new UserLimitsService();
        }
        return UserLimitsService.instance;
    }

    private getTodayString(): string {
        return new Date().toISOString().split("T")[0];
    }

    private getNextResetTime(): string {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return tomorrow.toISOString();
    }

    async getUserDailyLimit(userId: string): Promise<number> {
        try {
            const { data, error } = await supabaseAdmin.auth.admin.getUserById(
                userId,
            );

            if (error || !data?.user) {
                console.error("Error fetching user:", error);
                return 1; // Default limit
            }

            // Check user_metadata for daily_page_limit
            const dailyLimit = data.user.user_metadata?.daily_page_limit;
            return typeof dailyLimit === "number" && dailyLimit > 0
                ? dailyLimit
                : 1;
        } catch (error) {
            console.error("Error getting user daily limit:", error);
            return 1; // Default limit
        }
    }

    async setUserDailyLimit(userId: string, limit: number): Promise<boolean> {
        try {
            const { error } = await supabaseAdmin.auth.admin.updateUserById(
                userId,
                {
                    user_metadata: { daily_page_limit: limit },
                },
            );

            if (error) {
                console.error("Error updating user daily limit:", error);
                return false;
            }

            return true;
        } catch (error) {
            console.error("Error setting user daily limit:", error);
            return false;
        }
    }

    async getDailyUsage(userId: string): Promise<number> {
        try {
            const today = this.getTodayString();
            const startOfDay = `${today}T00:00:00.000Z`;
            const endOfDay = `${today}T23:59:59.999Z`;

            const { count, error } = await supabaseAdmin
                .from("logs")
                .select("*", { count: "exact", head: true })
                .eq("user_id", userId)
                .eq("status", "SUCCESS")
                .gte("created_at", startOfDay)
                .lte("created_at", endOfDay);

            if (error) {
                console.error("Error fetching daily usage:", error);
                return 0;
            }

            return count ?? 0;
        } catch (error) {
            console.error("Error getting daily usage:", error);
            return 0;
        }
    }

    async getUserLimits(userId: string): Promise<UserLimits> {
        const dailyPageLimit = await this.getUserDailyLimit(userId);
        const dailyUsage = await this.getDailyUsage(userId);
        const remainingToday = Math.max(0, dailyPageLimit - dailyUsage);
        const nextResetTime = this.getNextResetTime();

        return {
            dailyPageLimit,
            dailyUsage,
            remainingToday,
            nextResetTime,
        };
    }

    async canUserGeneratePage(userId: string): Promise<boolean> {
        const limits = await this.getUserLimits(userId);
        return limits.remainingToday > 0;
    }

    async consumePageGeneration(userId: string): Promise<boolean> {
        const canGenerate = await this.canUserGeneratePage(userId);
        if (canGenerate) {
            // Note: The actual consumption is tracked when a SUCCESS log is inserted
            // This method just checks if generation is allowed
            return true;
        }
        return false;
    }

    async resetDailyUsage(userId: string): Promise<boolean> {
        try {
            const today = this.getTodayString();
            const startOfDay = `${today}T00:00:00.000Z`;
            const endOfDay = `${today}T23:59:59.999Z`;

            const { error } = await supabaseAdmin
                .from("logs")
                .delete()
                .eq("user_id", userId)
                .eq("status", "SUCCESS")
                .gte("created_at", startOfDay)
                .lte("created_at", endOfDay);

            if (error) {
                console.error("Error resetting daily usage:", error);
                return false;
            }

            return true;
        } catch (error) {
            console.error("Error resetting daily usage:", error);
            return false;
        }
    }
}
