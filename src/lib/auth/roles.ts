import { createClient } from "@/lib/supabase/server";
import { supabaseAdmin } from "@/db/supabase";
import { UserRole, UserWithRole } from "@/types";

/**
 * Get user role from user metadata
 */
export async function getCurrentUserRole(): Promise<UserRole> {
    const supabase = await createClient();

    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
        return "subscriber"; // Default role
    }

    return (user.user_metadata?.role as UserRole) ?? "subscriber";
}

/**
 * Get user role by user ID
 *
 * ⚠️ SECURITY: This function uses the service role key and bypasses RLS.
 * It should ONLY be called on the backend (server-side) to avoid exposing
 * the service role key to the browser.
 */
export async function getUserRole(userId: string): Promise<UserRole> {
    const { data, error } = await supabaseAdmin.auth.admin.getUserById(userId);

    if (error || !data?.user) {
        return "subscriber"; // Default role
    }

    return (data.user.user_metadata?.role as UserRole) ?? "subscriber";
}

/**
 * Get current user with role
 */
export async function getCurrentUserWithRole(): Promise<UserWithRole | null> {
    const supabase = await createClient();

    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
        return null;
    }

    const role = (user.user_metadata?.role as UserRole) ?? "subscriber";

    return {
        id: user.id,
        email: user.email,
        role,
    };
}

/**
 * Check if user is admin
 *
 * ⚠️ SECURITY: This function uses the service role key and should only be called on the backend.
 */
export async function isAdmin(userId: string): Promise<boolean> {
    const role = await getUserRole(userId);
    return role === "admin";
}

/**
 * Check if current user is admin
 */
export async function isCurrentUserAdmin(): Promise<boolean> {
    const userWithRole = await getCurrentUserWithRole();
    return userWithRole?.role === "admin";
}

/**
 * Require admin role - throws error if not admin
 *
 * ⚠️ SECURITY: This function uses the service role key and should only be called on the backend.
 */
export async function requireAdmin(userId: string): Promise<void> {
    const isUserAdmin = await isAdmin(userId);

    if (!isUserAdmin) {
        throw new Error("Admin access required");
    }
}

/**
 * Require current user to be admin
 */
export async function requireCurrentUserAdmin(): Promise<void> {
    const isUserAdmin = await isCurrentUserAdmin();

    if (!isUserAdmin) {
        throw new Error("Admin access required");
    }
}
