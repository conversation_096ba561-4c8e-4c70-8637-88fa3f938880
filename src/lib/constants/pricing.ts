/**
 * Platform pricing configuration
 * Centralized pricing constants to maintain consistency across the application
 */

export interface PlatformPricing {
    pricePerPage: number;
    currency: string;
    description: string;
}

export const PLATFORM_PRICING: PlatformPricing = {
    pricePerPage: 0.5, // $0.50 per page
    currency: "USD",
    description: "Platform pricing per generated page",
};

/**
 * Environment variable fallback for platform price per page
 * Used in server-side calculations with environment override capability
 */
export const getPlatformPricePerPage = (): number => {
    const value = Number(process.env.PLATFORM_PRICE_PER_PAGE);
    return isNaN(value) ? PLATFORM_PRICING.pricePerPage : value;
};
