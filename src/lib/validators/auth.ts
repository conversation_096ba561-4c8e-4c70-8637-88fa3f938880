import { z } from "zod";

// DTO for login form
export interface LoginFormData {
    email: string;
    password: string;
}

// DTO for registration form
export interface RegisterFormData {
    email: string;
    password: string;
    confirmPassword: string;
    // CAPTCHA token will likely be added/verified in the Server Action
}

// DTO for forgot password form
export interface ForgotPasswordFormData {
    email: string;
}

// DTO for reset password form
export interface ResetPasswordFormData {
    password: string;
    confirmPassword: string;
    // Reset token will be required in the Server Action, obtained from context
}

// ViewModel for Server Action results
export interface AuthActionResult {
    success: boolean;
    message?: string; // General message (e.g., success, server error)
    fieldErrors?: Record<string, string[] | undefined>; // Form field-specific errors
    redirect?: string; // Optional redirect path on success
}

// Zod Schemas for validation
export const LoginSchema = z.object({
    email: z.string().email({ message: "Invalid email format." }),
    password: z.string().min(1, { message: "Password is required." }),
});

const passwordValidation = z
    .string()
    .min(8, { message: "Password must be at least 8 characters long." })
    .regex(/[A-Z]/, {
        message: "Password must contain at least one uppercase letter.",
    })
    .regex(/[a-z]/, {
        message: "Password must contain at least one lowercase letter.",
    })
    .regex(/[0-9]/, { message: "Password must contain at least one digit." })
    .regex(/[^A-Za-z0-9]/, {
        message: "Password must contain at least one special character.",
    });

export const RegisterSchema = z
    .object({
        email: z.string().email({ message: "Invalid email format." }),
        password: passwordValidation,
        confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Passwords do not match.",
        path: ["confirmPassword"], // Error path for the confirmation field
    });

export const ForgotPasswordSchema = z.object({
    email: z.string().email({ message: "Invalid email format." }),
});

export const ResetPasswordSchema = z
    .object({
        password: passwordValidation,
        confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Passwords do not match.",
        path: ["confirmPassword"],
    });
