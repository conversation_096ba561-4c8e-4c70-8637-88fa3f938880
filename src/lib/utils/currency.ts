/**
 * Format currency values with appropriate decimal places
 * For small amounts (under $1, excluding zero), use 4 decimal places
 * For zero and larger amounts, use 2 decimal places
 */
export const formatCurrency = (
    value: number,
    currency: string = "USD",
): string => {
    // Validate input - check for NaN, Infinity, and other invalid numbers
    if (!Number.isFinite(value)) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(0);
    }

    // Use 4 decimal places for small absolute values (under $1, excluding zero)
    // Use 2 decimal places for zero and values with absolute value >= 1
    const absValue = Math.abs(value);
    const decimalPlaces = absValue > 0 && absValue < 1 ? 4 : 2;

    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: currency,
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces,
    }).format(value);
};
