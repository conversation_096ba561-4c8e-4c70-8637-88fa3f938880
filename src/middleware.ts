import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "./lib/supabase/middleware";
import { createServerClient } from "@supabase/ssr";
import { UserRole } from "./types";

// Helper function to validate UserRole
const isValidUserRole = (role: unknown): role is UserRole => {
    return typeof role === "string" &&
        (role === "admin" || role === "subscriber");
};

export async function middleware(request: NextRequest) {
    // Update session first
    const response = await updateSession(request);

    // Check for admin routes
    if (request.nextUrl.pathname.startsWith("/admin")) {
        // Validate required environment variables
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

        if (!supabaseUrl) {
            throw new Error(
                "NEXT_PUBLIC_SUPABASE_URL is not defined. Please check your environment variables.",
            );
        }

        if (!supabaseAnonKey) {
            throw new Error(
                "NEXT_PUBLIC_SUPABASE_ANON_KEY is not defined. Please check your environment variables.",
            );
        }

        const supabase = createServerClient(
            supabaseUrl,
            supabaseAnonKey,
            {
                cookies: {
                    getAll() {
                        return request.cookies.getAll();
                    },
                    setAll(cookiesToSet) {
                        cookiesToSet.forEach(({ name, value, options }) => {
                            response.cookies.set(name, value, options);
                        });
                    },
                },
            },
        );

        const { data: { user }, error } = await supabase.auth.getUser();

        if (error || !user) {
            return NextResponse.redirect(new URL("/login", request.url));
        }

        // Safely validate and extract user role
        const rawRole = user.user_metadata?.role;
        const userRole: UserRole = isValidUserRole(rawRole)
            ? rawRole
            : "subscriber";

        if (userRole !== "admin") {
            return NextResponse.redirect(new URL("/dashboard", request.url));
        }
    }

    return response;
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * Feel free to modify this pattern to include more paths.
         */
        "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
    ],
};
