"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { createClient } from "@/lib/supabase/client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  UserCircleIcon,
  KeyIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";

export default function Profile() {
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [createdAt, setCreatedAt] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const supabase = createClient();

        // Get user data
        const {
          data: { user },
          error: userError,
        } = await supabase.auth.getUser();

        if (userError) {
          throw userError;
        }

        if (user) {
          setUserEmail(user.email || null);
          // Format the created_at date for display
          if (user.created_at) {
            const date = new Date(user.created_at);
            setCreatedAt(date.toLocaleDateString());
          }
        }
      } catch (err) {
        console.error("Error fetching user data:", err);
        setError("Failed to load user profile information.");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  if (loading) {
    return (
      <main className="container pb-10 mx-auto pt-12">
        <div className="flex flex-col items-center justify-center min-h-[300px]">
          <span className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-400 mb-4" />
          <span className="text-muted-foreground">Loading profile...</span>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="container pb-10 mx-auto pt-12">
        <div className="flex flex-col items-center justify-center min-h-[300px]">
          <span className="text-red-500 mb-4">{error}</span>
          <Button asChild>
            <Link href="/dashboard">Return to Dashboard</Link>
          </Button>
        </div>
      </main>
    );
  }

  return (
    <main className="container pb-10 mx-auto">
      <section className="flex flex-col items-center space-y-6 mb-8 pt-12">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
          <p className="text-muted-foreground mt-2">
            View and manage your account information
          </p>
        </div>
      </section>

      <section className="max-w-3xl mx-auto">
        <Card className="p-6">
          <div className="flex items-center gap-4 mb-6">
            <UserCircleIcon className="w-16 h-16 text-primary" />
            <div>
              <h2 className="text-2xl font-semibold">{userEmail}</h2>
              {createdAt && (
                <p className="text-muted-foreground">
                  Member since {createdAt}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4 mt-8">
            <h3 className="text-lg font-medium">Account Management</h3>
            <div className="grid gap-4">
              <Button variant="outline" asChild className="justify-start">
                <Link
                  href="/change-password"
                  className="flex items-center gap-2"
                >
                  <KeyIcon className="w-5 h-5" />
                  Change Password
                </Link>
              </Button>

              <Button variant="destructive" asChild className="justify-start">
                <Link
                  href="/delete-account"
                  className="flex items-center gap-2"
                >
                  <TrashIcon className="w-5 h-5" />
                  Delete Account
                </Link>
              </Button>
            </div>
          </div>
        </Card>
      </section>
    </main>
  );
}
