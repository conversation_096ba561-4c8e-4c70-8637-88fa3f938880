"use client";

import Link from "next/link";
import {
  UserIcon,
  UserPlusIcon,
  RectangleGroupIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  ClockIcon,
  CogIcon,
} from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useUserRole } from "@/hooks/useUserRole";

export default function Header() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const { isAdmin } = useUserRole();
  const router = useRouter();

  useEffect(() => {
    const supabase = createClient();

    const checkAuth = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      setIsAuthenticated(!!session);

      if (session?.user) {
        setUserEmail(session.user.email || null);
      }
    };

    checkAuth();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setIsAuthenticated(!!session);
      setUserEmail(session?.user?.email || null);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Helper function to get user initials for avatar
  const getUserInitials = () => {
    if (!userEmail) return "U";
    return userEmail.substring(0, 1).toUpperCase();
  };

  const handleLogout = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    router.push("/login");
  };

  return (
    <header className="bg-background/80 backdrop-blur-sm fixed top-0 left-0 right-0 z-50">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <Link
          href="/"
          className="text-2xl flex items-center gap-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-primary"
          >
            {/* Outline with sharp bottom left corner */}
            <path
              d="M3 29 L3 8 Q3 3 8 3 L24 3 Q29 3 29 8 L29 24 Q29 29 24 29 L8 29 Q8 29 3 29 Z"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
            />
            {/* Upward arrow from bottom left corner */}
            <path
              d="M4 28 L21 11"
              stroke="#000000"
              strokeWidth="3"
              strokeLinecap="round"
            />
            {/* Arrow head */}
            <path
              d="M16 11 L21 11 L21 16"
              stroke="#000000"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
            />
          </svg>
          <span className="uppercase">
            <span className="font-thin">LANDING PAGE</span>{" "}
            <span className="font-bold">NOW</span>
          </span>
        </Link>
        <div className="flex gap-8">
          {isAuthenticated ? (
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger className="flex items-center gap-2 hover:opacity-80 focus:outline-none cursor-pointer">
                <Avatar>
                  <AvatarFallback>{getUserInitials()}</AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link
                    href="/dashboard"
                    className="flex items-center gap-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                  >
                    <RectangleGroupIcon className="w-4 h-4" />
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href="/history"
                    className="flex items-center gap-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                  >
                    <ClockIcon className="w-4 h-4" />
                    History
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href="/profile"
                    className="flex items-center gap-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                  >
                    <UserCircleIcon className="w-4 h-4" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                {isAdmin && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link
                        href="/admin"
                        className="flex items-center gap-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                      >
                        <CogIcon className="w-4 h-4" />
                        Admin
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="flex items-center gap-2 text-destructive cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                >
                  <ArrowRightOnRectangleIcon className="w-4 h-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <>
              <Link
                className="flex items-center gap-2 hover:underline hover:underline-offset-4 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                href="/login"
              >
                <UserIcon className="w-4 h-4" />
                Login
              </Link>
              <Link
                className="flex items-center gap-2 hover:underline hover:underline-offset-4 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                href="/register"
              >
                <UserPlusIcon className="w-4 h-4" />
                Register
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
