"use client";

import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { createClient } from "@/lib/supabase/client";
import { useUserRole } from "@/hooks/useUserRole";
import { AdminOnly } from "@/components/auth/RoleGuard";
import { formatCurrency } from "@/lib/utils/currency";
import { PLATFORM_PRICING } from "@/lib/constants/pricing";

interface CostData {
  totalCost: number;
  last30DaysCost: number;
  currentMonthCost: number;
  currency: string;
  modelBreakdown?: Array<{
    model: string;
    cost: number;
    count: number;
    tokens: number;
    avgCostPerRequest: number;
  }>;
  requestCount?: number;
  totalTokens?: number;
  totalAttempts?: number;
  avgAttemptsPerRequest?: number;
  totalCumulativeCost?: number;
  savingsPercentage?: number;
}

export function CostTracker() {
  const [costData, setCostData] = useState<CostData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAdmin } = useUserRole();

  useEffect(() => {
    async function fetchCostData() {
      try {
        // Get session for auth
        const supabase = createClient();
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session) {
          setError("Authentication required");
          setLoading(false);
          return;
        }

        const response = await fetch("/api/logs", {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${session.access_token}`,
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch cost data: ${response.statusText}`);
        }

        const data = await response.json();
        setCostData(data);
      } catch (err) {
        console.error("Error fetching cost data:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load cost data"
        );
      } finally {
        setLoading(false);
      }
    }

    fetchCostData();
  }, []);

  // Calculate platform pricing for subscribers
  const calculatePlatformCosts = (requestCount: number) => {
    const totalPlatformCost = requestCount * PLATFORM_PRICING.pricePerPage;
    return {
      total: totalPlatformCost,
      perPage: PLATFORM_PRICING.pricePerPage,
      currency: PLATFORM_PRICING.currency,
    };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isAdmin ? "Cost Tracking (Admin View)" : "Usage & Pricing"}
        </CardTitle>
        <CardDescription>
          {isAdmin
            ? "Track actual page generation costs and platform margins"
            : "Your page generation usage and platform pricing"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading && (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
        )}

        {error && <div className="text-red-500">{error}</div>}

        {costData && !loading && !error && (
          <div className="space-y-6">
            {isAdmin ? (
              // Admin view - show real costs
              <AdminOnly>
                <div className="grid gap-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      Current Month (Real Cost):
                    </span>
                    <span className="font-bold">
                      {formatCurrency(
                        costData.currentMonthCost,
                        costData.currency
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      Last 30 Days (Real Cost):
                    </span>
                    <span className="font-bold">
                      {formatCurrency(
                        costData.last30DaysCost,
                        costData.currency
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      All Time (Real Cost):
                    </span>
                    <span className="font-bold">
                      {formatCurrency(costData.totalCost, costData.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      Platform Revenue (All Time):
                    </span>
                    <span className="font-bold text-green-600">
                      {formatCurrency(
                        calculatePlatformCosts(costData.requestCount || 0)
                          .total - costData.totalCost,
                        PLATFORM_PRICING.currency
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Requests:</span>
                    <span className="font-bold">
                      {costData.requestCount?.toLocaleString() || 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Tokens:</span>
                    <span className="font-bold">
                      {costData.totalTokens?.toLocaleString() || 0}
                    </span>
                  </div>
                </div>

                {costData.modelBreakdown &&
                  costData.modelBreakdown.length > 0 && (
                    <div className="mt-6">
                      <h4 className="text-sm font-medium mb-3">
                        Model Breakdown (Admin)
                      </h4>
                      <div className="space-y-3">
                        {costData.modelBreakdown.map((model) => (
                          <div
                            key={model.model}
                            className="border p-3 rounded-md"
                          >
                            <div className="flex justify-between mb-1">
                              <span className="font-medium">{model.model}</span>
                              <span>
                                {formatCurrency(model.cost, costData.currency)}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 gap-1 text-xs text-muted-foreground">
                              <div>Requests: {model.count}</div>
                              <div>Tokens: {model.tokens.toLocaleString()}</div>
                              <div className="col-span-2">
                                Avg. Cost per Request:{" "}
                                {formatCurrency(
                                  model.avgCostPerRequest,
                                  costData.currency
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </AdminOnly>
            ) : (
              // Subscriber view - show platform pricing
              <div className="grid gap-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Price per Page:</span>
                  <span className="font-bold">
                    {formatCurrency(
                      PLATFORM_PRICING.pricePerPage,
                      PLATFORM_PRICING.currency
                    )}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Pages Generated:</span>
                  <span className="font-bold">
                    {costData.requestCount?.toLocaleString() || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Value:</span>
                  <span className="font-bold">
                    {formatCurrency(
                      calculatePlatformCosts(costData.requestCount || 0).total,
                      PLATFORM_PRICING.currency
                    )}
                  </span>
                </div>

                <div className="mt-4 p-3 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-800">
                    💡 You&apos;re using our premium AI-powered landing page
                    generator. Each page is crafted with advanced AI models to
                    ensure high-quality, conversion-optimized content.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
