"use client";

import { useEffect, useState, forwardRef, useImperative<PERSON><PERSON>le } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface RateLimitInfo {
  dailyUsage: number;
  dailyLimit: number;
  remainingToday: number;
  nextResetTime: string;
  nextResetIn: string;
  currentTokens: number;
  maxTokens: number;
}

interface CustomProgressStyle extends React.CSSProperties {
  "--progress-color": string;
}

export interface RateLimitInfoRef {
  refresh: () => void;
}

export const RateLimitInfo = forwardRef<RateLimitInfoRef>((props, ref) => {
  const [rateLimitInfo, setRateLimitInfo] = useState<RateLimitInfo | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRateLimit = async () => {
    try {
      const response = await fetch("/api/rate-limit");
      if (!response.ok) throw new Error("Failed to fetch rate limit info");
      const data = await response.json();
      setRateLimitInfo(data);
    } catch (err) {
      setError("Could not load rate limit information");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    refresh: fetchRateLimit,
  }));

  useEffect(() => {
    fetchRateLimit();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Daily Generation Limit</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-2 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Daily Generation Limit</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-500">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!rateLimitInfo) return null;

  const dailyPercentage =
    (rateLimitInfo.dailyUsage / rateLimitInfo.dailyLimit) * 100;
  const warningThreshold = rateLimitInfo.dailyLimit <= 3 ? 66 : 80; // Lower threshold for small limits

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Daily Limit</span>
          <span className="text-xs text-muted-foreground">
            Resets every day at midnight
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div>
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium">Pages Generated Today</span>
            <span className="text-sm text-muted-foreground">
              {rateLimitInfo.dailyUsage}/{rateLimitInfo.dailyLimit}
            </span>
          </div>
          <Progress
            value={dailyPercentage}
            className="h-2"
            style={
              {
                backgroundColor:
                  dailyPercentage > warningThreshold ? "#fee2e2" : "#f3f4f6",
                "--progress-color":
                  dailyPercentage > warningThreshold ? "#ef4444" : "#3b82f6",
              } as CustomProgressStyle
            }
          />

          {rateLimitInfo.remainingToday === 0 && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
              <p className="text-xs text-red-700">
                Daily limit reached. Your limit will reset tomorrow at midnight.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

RateLimitInfo.displayName = "RateLimitInfo";
