"use client";
import { useState, useCallback, useRef, useMemo } from "react";
import { GeneratePageForm } from "@/components/page-generator";
import {
  ResponsivePreview,
  ViewportControls,
} from "@/components/ui/ResponsivePreview";
import { FullscreenPreview } from "@/components/ui/FullscreenPreview";
import Link from "next/link";
import { ClockIcon, ClipboardDocumentIcon } from "@heroicons/react/24/outline";
import { RateLimitInfo, RateLimitInfoRef } from "./RateLimitInfo";
import { formatHtml } from "@/services/page-generator/html-processor";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneLight } from "react-syntax-highlighter/dist/esm/styles/prism";

type PreviewTabsProps = {
  html: string | null;
  loading: boolean;
  error: string | null;
  onRetry?: () => void;
};

function PreviewTabs({ html, loading, error, onRetry }: PreviewTabsProps) {
  const [activeTab, setActiveTab] = useState("preview");
  const [activeViewport, setActiveViewport] = useState<
    "desktop" | "tablet" | "mobile"
  >("desktop");
  const [orientation, setOrientation] = useState<"portrait" | "landscape">(
    "portrait"
  );
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Memoize formatted HTML to avoid recomputation on every render
  const formattedHtml = useMemo(() => {
    return html ? formatHtml(html) : "";
  }, [html]);

  const handleCopyCode = useCallback(async () => {
    if (!formattedHtml) return;

    try {
      await navigator.clipboard.writeText(formattedHtml);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error("Failed to copy code:", err);
    }
  }, [formattedHtml]);

  // Reset to portrait when switching between tablet and mobile
  const handleViewportChange = (
    newViewport: "desktop" | "tablet" | "mobile"
  ) => {
    setActiveViewport(newViewport);
    if (newViewport === "tablet" || newViewport === "mobile") {
      setOrientation("portrait");
    }
  };

  // Dynamic height based on viewport and orientation
  const getContainerHeight = () => {
    switch (activeViewport) {
      case "tablet":
        return orientation === "portrait" ? "min-h-[1024px]" : "min-h-[768px]";
      case "mobile":
        return orientation === "portrait" ? "min-h-[667px]" : "min-h-[375px]";
      default:
        return "min-h-[600px]"; // desktop
    }
  };

  if (loading) {
    return (
      <div
        className={`flex flex-col items-center justify-center w-full h-full ${getContainerHeight()}`}
      >
        <span className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-400 mb-4" />
        <span className="text-muted-foreground">Generating...</span>
      </div>
    );
  }
  if (error) {
    return (
      <div
        className={`flex flex-col items-center justify-center w-full h-full ${getContainerHeight()}`}
      >
        <span className="text-red-500 mb-2">{error}</span>
        {onRetry && (
          <button
            className="btn btn-outline cursor-pointer hover:cursor-pointer focus:cursor-pointer"
            onClick={onRetry}
          >
            Retry
          </button>
        )}
      </div>
    );
  }
  if (!html) {
    return (
      <div
        className={`flex flex-col items-center justify-center w-full h-full ${getContainerHeight()}`}
      >
        <span className="text-muted-foreground">
          Generate your landing page to see the preview
        </span>
      </div>
    );
  }
  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex border-b mb-2 justify-between items-center">
        <div className="flex">
          <button
            className={`px-4 py-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer ${
              activeTab === "preview"
                ? "border-b-2 border-primary font-bold"
                : "text-muted-foreground"
            }`}
            onClick={() => setActiveTab("preview")}
          >
            Preview
          </button>
          <button
            className={`px-4 py-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer ${
              activeTab === "code"
                ? "border-b-2 border-primary font-bold"
                : "text-muted-foreground"
            }`}
            onClick={() => setActiveTab("code")}
          >
            Code
          </button>
        </div>
        {activeTab === "preview" && (
          <ViewportControls
            activeViewport={activeViewport}
            onViewportChange={handleViewportChange}
            orientation={orientation}
            onOrientationChange={setOrientation}
            onFullscreen={() => setIsFullscreen(true)}
          />
        )}
      </div>
      <div
        className={`flex-1 overflow-auto bg-white border p-4 ${getContainerHeight()} rounded-b-lg`}
      >
        {activeTab === "preview" ? (
          <ResponsivePreview
            html={html}
            activeViewport={activeViewport}
            orientation={orientation}
          />
        ) : (
          <div className="h-full overflow-auto relative">
            {activeTab === "code" && (
              <>
                <button
                  onClick={handleCopyCode}
                  className="absolute top-2 right-2 z-10 p-2 bg-white/90 hover:bg-white border border-gray-200 rounded-md shadow-sm transition-colors cursor-pointer"
                  title={copySuccess ? "Copied!" : "Copy code"}
                >
                  <ClipboardDocumentIcon className="w-4 h-4 text-gray-600" />
                </button>
                {copySuccess && (
                  <div className="absolute top-2 right-16 z-10 px-3 py-1 bg-green-500 text-white text-sm rounded-md shadow-sm">
                    Copied!
                  </div>
                )}
              </>
            )}
            <SyntaxHighlighter
              language="html"
              style={oneLight}
              customStyle={{
                margin: 0,
                height: "100%",
                fontSize: "12px",
                lineHeight: "1.4",
              }}
              showLineNumbers={true}
              wrapLines={true}
              wrapLongLines={true}
            >
              {formattedHtml}
            </SyntaxHighlighter>
          </div>
        )}
      </div>

      <FullscreenPreview
        html={html || ""}
        isOpen={isFullscreen}
        onClose={() => setIsFullscreen(false)}
        title="Landing Page"
      />
    </div>
  );
}

export default function Dashboard() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [html, setHtml] = useState<string | null>(null);
  const rateLimitInfoRef = useRef<RateLimitInfoRef>(null);
  const POLL_INTERVAL = 2000;
  const MAX_POLLS = 30;

  const pollForHtml = useCallback(async (id: number, attempt = 0) => {
    if (attempt > MAX_POLLS) {
      setLoading(false);
      setError("Generation timed out. Please try again.");
      return;
    }
    try {
      const res = await fetch(`/api/preview/${id}`);
      if (!res.ok) throw new Error("Failed to fetch page");
      const data = await res.json();
      if (data.html_content) {
        setHtml(data.html_content);
        setLoading(false);
        setError(null);
      } else if (data.status === "FAILED") {
        setLoading(false);
        setError("Generation failed. Please try again.");
      } else {
        setTimeout(() => pollForHtml(id, attempt + 1), POLL_INTERVAL);
      }
    } catch {
      setLoading(false);
      setError("Error fetching generated page.");
    }
  }, []);

  return (
    <main className="container pb-10 mx-auto">
      <section className="flex flex-col items-center space-y-6 mb-8 pt-12">
        <div className="text-center mb-6">
          <h1
            className="text-3xl font-bold tracking-tight"
            data-testid="dashboard-heading"
          >
            Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Create a professional landing page in minutes using AI
          </p>
        </div>
        <div className="flex justify-center">
          <Link
            href="/history"
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary border border-primary rounded-md hover:bg-primary/10 transition-colors"
          >
            <ClockIcon className="w-4 h-4" />
            View Generated Pages History
          </Link>
        </div>
      </section>

      <section className="flex flex-col md:flex-row gap-8 w-full mx-auto">
        <div className="w-full md:w-1/3 lg:w-1/4 space-y-4">
          <RateLimitInfo ref={rateLimitInfoRef} />
          <GeneratePageForm
            onResult={(result) => {
              if (result.html) {
                setHtml(result.html);
                setLoading(false);
                setError(null);
                // Refresh the rate limit info after successful generation
                rateLimitInfoRef.current?.refresh();
              } else if (result.loading) {
                setLoading(true);
                setError(null);
              } else if (result.error) {
                setError(result.error);
                setLoading(false);
              }
            }}
          />
        </div>
        <div className="hidden md:block w-0 md:w-2/3 bg-muted rounded-lg min-h-[600px] flex items-center justify-center border border-dashed border-gray-300">
          <PreviewTabs
            html={html}
            loading={loading}
            error={error}
            onRetry={
              html
                ? undefined
                : () => {
                    /* no-op or custom retry logic */
                  }
            }
          />
        </div>
      </section>
    </main>
  );
}
