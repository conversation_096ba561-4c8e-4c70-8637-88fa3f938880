"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { createClient } from "@/lib/supabase/client";
import { AdminOnly } from "@/components/auth/RoleGuard";
import { useUserRole } from "@/hooks/useUserRole";
import { PLATFORM_PRICING } from "@/lib/constants/pricing";

/**
 * Type for the log data
 */
interface LogEntry {
  id: string;
  created_at: string;
  status: string;
  model: string | null;
  prompt_tokens: number | null;
  completion_tokens: number | null;
  total_tokens: number | null;
  prompt_cost: number | null;
  completion_cost: number | null;
  total_cost: number | null;
  cost_currency: string | null;
  error_message: string | null;
  generation_duration_ms: number | null;
  generation_attempts: number | null;
  cumulative_tokens: number | null;
  cumulative_cost: number | null;
}

/**
 * A debug component to display detailed usage information
 * Shows different views for admins vs subscribers
 */
export function UsageDebugInfo() {
  const [latestLogs, setLatestLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAdmin } = useUserRole();

  useEffect(() => {
    if (!isAdmin) {
      setLoading(false);
      return;
    }

    async function fetchLatestLogs() {
      try {
        const supabase = createClient();
        const { data: session } = await supabase.auth.getSession();

        if (!session?.session) {
          setError("Authentication required");
          setLoading(false);
          return;
        }

        // Get the latest 5 logs
        const { data, error } = await supabase
          .from("logs")
          .select("*")
          .order("created_at", { ascending: false })
          .limit(5);

        if (error) {
          throw new Error(`Failed to fetch logs: ${error.message}`);
        }

        setLatestLogs(data || []);
      } catch (err) {
        console.error("Error fetching logs:", err);
        setError(err instanceof Error ? err.message : "Failed to load logs");
      } finally {
        setLoading(false);
      }
    }

    fetchLatestLogs();
  }, [isAdmin]);

  return (
    <AdminOnly
      fallback={
        <Card className="mt-4">
          <CardHeader>
            <CardTitle>Usage Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Your recent page generations are processed using our premium AI
                models. Each generation is optimized for quality and conversion.
              </p>
              <div className="p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  💡 For detailed usage analytics and system insights, upgrade
                  to our admin plan.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      }
    >
      <Card className="mt-4">
        <CardHeader>
          <CardTitle>Usage Debug Information (Admin)</CardTitle>
        </CardHeader>
        <CardContent>
          {loading && <p>Loading usage data...</p>}
          {error && <p className="text-red-500">{error}</p>}

          {!loading && !error && latestLogs.length === 0 && (
            <p>No logs found</p>
          )}

          {!loading && !error && latestLogs.length > 0 && (
            <div className="space-y-4">
              {latestLogs.map((log) => (
                <div key={log.id} className="border p-3 rounded-md">
                  <h3 className="font-medium">Log ID: {log.id}</h3>
                  <p className="text-sm">
                    Created: {new Date(log.created_at).toLocaleString()}
                  </p>
                  <p className="text-sm">Status: {log.status}</p>
                  <p className="text-sm">Model: {log.model || "Unknown"}</p>
                  <p className="text-sm">
                    Generation Time:{" "}
                    {log.generation_duration_ms
                      ? `${(log.generation_duration_ms / 1000).toFixed(2)}s`
                      : "N/A"}
                  </p>
                  <p className="text-sm">
                    Attempts: {log.generation_attempts || 1}
                  </p>

                  <div className="mt-2">
                    <h4 className="text-sm font-medium">Last Attempt:</h4>
                    <p className="text-xs">
                      Tokens: {log.total_tokens?.toLocaleString() || 0} (Prompt:{" "}
                      {log.prompt_tokens?.toLocaleString() || 0}, Completion:{" "}
                      {log.completion_tokens?.toLocaleString() || 0})
                    </p>
                    <p className="text-xs">
                      Real Cost: ${log.total_cost?.toFixed(6) || 0}{" "}
                      {log.cost_currency}
                    </p>
                    <p className="text-xs text-green-600">
                      Platform Price: $
                      {PLATFORM_PRICING.pricePerPage.toFixed(2)}{" "}
                      {PLATFORM_PRICING.currency}
                    </p>
                    <p className="text-xs text-green-600">
                      Margin: $
                      {(
                        PLATFORM_PRICING.pricePerPage - (log.total_cost || 0)
                      ).toFixed(4)}{" "}
                      {PLATFORM_PRICING.currency}
                    </p>
                  </div>

                  {(log.generation_attempts || 0) > 1 && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium">
                        Cumulative (All Attempts):
                      </h4>
                      <p className="text-xs">
                        Total Tokens:{" "}
                        {log.cumulative_tokens?.toLocaleString() ||
                          log.total_tokens?.toLocaleString() ||
                          0}
                      </p>
                      <p className="text-xs">
                        Total Real Cost: $
                        {log.cumulative_cost?.toFixed(6) ||
                          log.total_cost?.toFixed(6) ||
                          0}{" "}
                        {log.cost_currency}
                      </p>
                      <p className="text-xs">
                        Avg. Cost Per Attempt: $
                        {(
                          (log.cumulative_cost || log.total_cost || 0) /
                          (log.generation_attempts || 1)
                        ).toFixed(6)}
                      </p>
                    </div>
                  )}

                  <div className="mt-2">
                    <h4 className="text-sm font-medium">Token Usage:</h4>
                    <p className="text-xs">Prompt: {log.prompt_tokens}</p>
                    <p className="text-xs">
                      Completion: {log.completion_tokens}
                    </p>
                    <p className="text-xs">Total: {log.total_tokens}</p>
                  </div>

                  <div className="mt-2">
                    <h4 className="text-sm font-medium">Cost Breakdown:</h4>
                    <p className="text-xs">
                      Prompt: ${log.prompt_cost?.toFixed(6)}
                    </p>
                    <p className="text-xs">
                      Completion: ${log.completion_cost?.toFixed(6)}
                    </p>
                    <p className="text-xs">
                      Total: ${log.total_cost?.toFixed(6)} {log.cost_currency}
                    </p>
                  </div>

                  {log.error_message && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-red-500">
                        Error:
                      </h4>
                      <p className="text-xs">{log.error_message}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </AdminOnly>
  );
}
