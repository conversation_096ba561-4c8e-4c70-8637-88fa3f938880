"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { createClient } from "@/lib/supabase/client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Link from "next/link";

// Schema for account deletion confirmation
const DeleteAccountSchema = z.object({
  password: z
    .string()
    .min(1, "Password is required to confirm account deletion"),
  confirmation: z.string().refine((val) => val === "DELETE", {
    message: 'Please type "DELETE" to confirm',
  }),
});

type FormValues = {
  password: string;
  confirmation: string;
};

export default function DeleteAccount() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(DeleteAccountSchema),
    defaultValues: {
      password: "",
      confirmation: "",
    },
  });

  async function onSubmit(data: FormValues) {
    try {
      setIsSubmitting(true);
      setError(null);

      const supabase = createClient();

      // First, verify password by attempting to sign in
      const { data: userData } = await supabase.auth.getUser();
      const userEmail = userData.user?.email;

      if (!userEmail) {
        setError("Unable to verify your account. Please try again later.");
        return;
      }

      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: userEmail,
        password: data.password,
      });

      if (signInError) {
        setError("Password is incorrect");
        return;
      }

      // Delete the user account
      // Note: This might not work if admin API is not available in client
      try {
        await supabase.auth.admin.deleteUser(userData.user?.id || "");
      } catch {
        // Fallback to signOut if admin API is not available
        await supabase.auth.signOut();
        router.push("/login?deleted=requested");
        return;
      }

      // Sign out and redirect to login
      await supabase.auth.signOut();
      router.push("/login?deleted=true");
    } catch (err) {
      console.error("Error deleting account:", err);
      setError("Failed to delete account. Please contact support.");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <main className="container pb-10 mx-auto">
      <section className="flex flex-col items-center space-y-6 mb-8 pt-12">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold tracking-tight">Delete Account</h1>
          <p className="text-muted-foreground mt-2">
            Permanently delete your account and all data
          </p>
        </div>
      </section>

      <section className="max-w-md mx-auto">
        <Card className="p-6">
          <div className="mb-6 p-4 bg-destructive/10 text-destructive rounded-md">
            <p className="font-medium">Warning: This action cannot be undone</p>
            <p className="mt-2 text-sm">
              Deleting your account will remove all your data from our system
              permanently. You will not be able to recover your account or any
              of your data.
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <div className="p-3 bg-destructive/10 text-destructive rounded-md text-sm">
                  {error}
                </div>
              )}

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter your password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmation</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Type "DELETE" to confirm'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-between pt-2">
                <Button variant="outline" asChild>
                  <Link href="/profile">Cancel</Link>
                </Button>
                <Button
                  type="submit"
                  variant="destructive"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Deleting..." : "Delete My Account"}
                </Button>
              </div>
            </form>
          </Form>
        </Card>
      </section>
    </main>
  );
}
