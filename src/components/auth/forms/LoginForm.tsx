"use client";

import * as React from "react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { LoginSchema } from "@/lib/validators/auth";
import { login } from "@/actions/auth";
import { useAuthForm } from "@/hooks/useAuthForm";
import { BaseAuthForm } from "./BaseAuthForm";
import { Button } from "@/components/ui/button";
import { EmailField } from "./fields/EmailField";
import { PasswordField } from "./fields/PasswordField";
import { createClient } from "@/lib/supabase/client";

type LoginFormData = {
  email: string;
  password: string;
} & Record<string, unknown>;

export function LoginForm() {
  const { form, handleSubmit, isPending } = useAuthForm<LoginFormData>({
    schema: LoginSchema,
    defaultValues: {
      email: "",
      password: "",
    },
    action: login,
    onSuccess: () => redirect("/dashboard"),
  });
  const [isGoogleLoading, setIsGoogleLoading] = React.useState(false);

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    try {
      const supabase = createClient();
      const origin = window.location.origin;
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${origin}/auth/callback`,
        },
      });
      if (error) {
        // Optionally show a toast or error message
        setIsGoogleLoading(false);
      }
      // Supabase will redirect on success
    } catch {
      // Optionally show a toast or error message
      setIsGoogleLoading(false);
    }
  };

  const onSubmit = React.useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      return form.handleSubmit(handleSubmit)(e);
    },
    [form, handleSubmit]
  );

  return (
    <BaseAuthForm<LoginFormData>
      form={form}
      onSubmit={onSubmit}
      isPending={isPending}
      title="Sign in"
      description="Enter your email and password to access your account"
      footer={
        <>
          Don&apos;t have an account?{" "}
          <Link
            href="/register"
            className="underline cursor-pointer hover:cursor-pointer focus:cursor-pointer"
          >
            Sign up
          </Link>
        </>
      }
    >
      <Button
        type="button"
        variant="outline"
        className="w-full mb-4 flex items-center justify-center gap-2"
        onClick={handleGoogleSignIn}
        disabled={isGoogleLoading}
        data-testid="login-google"
      >
        {isGoogleLoading ? (
          <span>Signing in with Google...</span>
        ) : (
          <>
            <svg
              className="size-5"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_17_40)">
                <path
                  d="M23.766 12.276c0-.818-.074-1.604-.213-2.356H12.24v4.478h6.48a5.54 5.54 0 01-2.4 3.632v3.012h3.872c2.266-2.088 3.574-5.164 3.574-8.766z"
                  fill="#4285F4"
                />
                <path
                  d="M12.24 24c3.24 0 5.96-1.072 7.946-2.92l-3.872-3.012c-1.08.728-2.46 1.16-4.074 1.16-3.132 0-5.78-2.116-6.73-4.956H1.52v3.088A11.997 11.997 0 0012.24 24z"
                  fill="#34A853"
                />
                <path
                  d="M5.51 14.272A7.19 7.19 0 014.8 12c0-.792.136-1.56.376-2.272V6.64H1.52A12.004 12.004 0 000 12c0 1.896.454 3.692 1.52 5.36l3.99-3.088z"
                  fill="#FBBC05"
                />
                <path
                  d="M12.24 4.776c1.768 0 3.344.608 4.59 1.792l3.432-3.432C18.192 1.072 15.48 0 12.24 0A11.997 11.997 0 001.52 6.64l3.99 3.088c.95-2.84 3.598-4.952 6.73-4.952z"
                  fill="#EA4335"
                />
              </g>
              <defs>
                <clipPath id="clip0_17_40">
                  <rect width="24" height="24" fill="white" />
                </clipPath>
              </defs>
            </svg>
            <span>Sign in with Google</span>
          </>
        )}
      </Button>
      <hr className="my-4 border-t" />
      <EmailField form={form} name="email" testId="login-email" />
      <PasswordField form={form} name="password" testId="login-password" />
      <div className="flex items-center justify-end text-sm">
        <Link
          href="/forgot-password"
          className="underline underline-offset-4 hover:text-primary cursor-pointer hover:cursor-pointer focus:cursor-pointer"
          data-testid="login-forgot-password-link"
        >
          Forgot your password?
        </Link>
      </div>
      <Button
        type="submit"
        className="w-full"
        disabled={isPending}
        data-testid="login-submit"
      >
        {isPending ? "Signing in..." : "Sign in"}
      </Button>
    </BaseAuthForm>
  );
}
