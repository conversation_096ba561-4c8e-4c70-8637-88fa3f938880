"use client";

import * as React from "react";
import Link from "next/link";
import { RegisterSchema } from "@/lib/validators/auth";
import { register } from "@/actions/auth";
import { useAuthForm } from "@/hooks/useAuthForm";
import { useCaptcha } from "@/hooks/useCaptcha";
import { BaseAuthForm } from "./BaseAuthForm";
import { CaptchaWidget } from "../CaptchaWidget";
import { Button } from "@/components/ui/button";
import { EmailField } from "./fields/EmailField";
import { PasswordField } from "./fields/PasswordField";
import { toast } from "sonner";
import { createClient } from "@/lib/supabase/client";

type RegisterFormData = {
  email: string;
  password: string;
  confirmPassword: string;
} & Record<string, unknown>;

export function RegisterForm() {
  const { token, handleVerify, handleError, isVerified } = useCaptcha();

  const { form, handleSubmit, isPending } = useAuthForm<RegisterFormData>({
    schema: RegisterSchema,
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
    },
    action: async (data) => {
      if (!isVerified) {
        toast.error("CAPTCHA Required", {
          description: "Please complete the CAPTCHA challenge.",
        });
        return { success: false, message: "CAPTCHA verification required" };
      }
      return register(data, token!);
    },
  });

  const [isGoogleLoading, setIsGoogleLoading] = React.useState(false);

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    try {
      const supabase = createClient();
      const origin = window.location.origin;
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${origin}/auth/callback`,
        },
      });
      if (error) {
        setIsGoogleLoading(false);
      }
      // Supabase will redirect on success
    } catch {
      setIsGoogleLoading(false);
    }
  };

  const onSubmit = React.useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      return form.handleSubmit(handleSubmit)(e);
    },
    [form, handleSubmit]
  );

  return (
    <BaseAuthForm<RegisterFormData>
      form={form}
      onSubmit={onSubmit}
      isPending={isPending}
      title="Create an account"
      description="Enter your email below to create your account"
      footer={
        <>
          Already have an account?{" "}
          <Link href="/login" className="underline">
            Sign in
          </Link>
        </>
      }
    >
      <Button
        type="button"
        variant="outline"
        className="w-full mb-4 flex items-center justify-center gap-2"
        onClick={handleGoogleSignIn}
        disabled={isGoogleLoading}
        data-testid="register-google"
      >
        {isGoogleLoading ? (
          <span>Signing up with Google...</span>
        ) : (
          <>
            <svg
              className="size-5"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_17_40)">
                <path
                  d="M23.766 12.276c0-.818-.074-1.604-.213-2.356H12.24v4.478h6.48a5.54 5.54 0 01-2.4 3.632v3.012h3.872c2.266-2.088 3.574-5.164 3.574-8.766z"
                  fill="#4285F4"
                />
                <path
                  d="M12.24 24c3.24 0 5.96-1.072 7.946-2.92l-3.872-3.012c-1.08.728-2.46 1.16-4.074 1.16-3.132 0-5.78-2.116-6.73-4.956H1.52v3.088A11.997 11.997 0 0012.24 24z"
                  fill="#34A853"
                />
                <path
                  d="M5.51 14.272A7.19 7.19 0 014.8 12c0-.792.136-1.56.376-2.272V6.64H1.52A12.004 12.004 0 000 12c0 1.896.454 3.692 1.52 5.36l3.99-3.088z"
                  fill="#FBBC05"
                />
                <path
                  d="M12.24 4.776c1.768 0 3.344.608 4.59 1.792l3.432-3.432C18.192 1.072 15.48 0 12.24 0A11.997 11.997 0 001.52 6.64l3.99 3.088c.95-2.84 3.598-4.952 6.73-4.952z"
                  fill="#EA4335"
                />
              </g>
              <defs>
                <clipPath id="clip0_17_40">
                  <rect width="24" height="24" fill="white" />
                </clipPath>
              </defs>
            </svg>
            <span>Sign up with Google</span>
          </>
        )}
      </Button>
      <hr className="my-4 border-t" />
      <EmailField form={form} name="email" testId="register-email" />
      <PasswordField form={form} name="password" testId="register-password" />
      <PasswordField
        form={form}
        name="confirmPassword"
        label="Confirm Password"
        testId="register-confirm-password"
      />
      <div className="flex justify-center py-2">
        <CaptchaWidget onVerify={handleVerify} onError={handleError} />
      </div>
      <Button
        type="submit"
        className="w-full"
        disabled={isPending}
        data-testid="register-submit"
      >
        {isPending ? "Creating account..." : "Create account"}
      </Button>
    </BaseAuthForm>
  );
}
