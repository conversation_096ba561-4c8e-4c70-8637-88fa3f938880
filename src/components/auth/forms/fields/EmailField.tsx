import * as React from "react";
import {
  type UseFormReturn,
  type FieldValues,
  type Path,
} from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface EmailFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: Path<T>;
  testId?: string;
}

export function EmailField<T extends FieldValues>({
  form,
  name,
  testId,
}: EmailFieldProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input
              placeholder="<EMAIL>"
              type="email"
              {...field}
              data-testid={testId}
            />
          </FormControl>
          <FormMessage data-testid={`${testId}-error`} />
        </FormItem>
      )}
    />
  );
}
