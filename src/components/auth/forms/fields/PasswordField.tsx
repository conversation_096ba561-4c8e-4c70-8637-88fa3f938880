import * as React from "react";
import {
  type Path,
  type UseFormReturn,
  type FieldValues,
} from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface PasswordFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: Path<T>;
  label?: string;
  testId?: string;
}

export function PasswordField<T extends FieldValues>({
  form,
  name,
  label = "Password",
  testId,
}: PasswordFieldProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              placeholder="••••••••"
              type="password"
              {...field}
              data-testid={testId}
            />
          </FormControl>
          <FormMessage data-testid={`${testId}-error`} />
        </FormItem>
      )}
    />
  );
}
