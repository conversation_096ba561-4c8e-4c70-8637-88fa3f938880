"use client";

import * as React from "react";
import Link from "next/link";
import { ForgotPasswordSchema } from "@/lib/validators/auth";
import { requestPasswordReset } from "@/actions/auth";
import { useAuthForm } from "@/hooks/useAuthForm";
import { BaseAuthForm } from "./BaseAuthForm";
import { Button } from "@/components/ui/button";
import { EmailField } from "./fields/EmailField";

type ForgotPasswordFormData = {
  email: string;
} & Record<string, unknown>;

export function ForgotPasswordForm() {
  const { form, handleSubmit, isPending } = useAuthForm<ForgotPasswordFormData>(
    {
      schema: ForgotPasswordSchema,
      defaultValues: {
        email: "",
      },
      action: requestPasswordReset,
      onSuccess: () => {
        form.reset();
      },
    }
  );

  const onSubmit = React.useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      return form.handleSubmit(handleSubmit)(e);
    },
    [form, handleSubmit]
  );

  return (
    <BaseAuthForm<ForgotPasswordFormData>
      form={form}
      onSubmit={onSubmit}
      isPending={isPending}
      title="Forgot Password"
      description="Enter your email address and we'll send you a link to reset your password."
      footer={
        <Link
          href="/login"
          className="underline underline-offset-4 hover:text-primary"
        >
          Back to Sign in
        </Link>
      }
    >
      <EmailField form={form} name="email" testId="forgot-password-email" />
      <Button
        type="submit"
        className="w-full"
        disabled={isPending}
        data-testid="forgot-password-submit"
      >
        {isPending ? "Sending..." : "Send Reset Link"}
      </Button>
    </BaseAuthForm>
  );
}
