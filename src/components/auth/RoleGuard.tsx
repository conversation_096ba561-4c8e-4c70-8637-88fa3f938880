"use client";

import { ReactNode } from "react";
import { useUserRole } from "@/hooks/useUserRole";
import { UserRole } from "@/types";

interface RoleGuardProps {
  allowedRoles: UserRole[];
  children: ReactNode;
  fallback?: ReactNode;
  showLoading?: boolean;
}

export function RoleGuard({
  allowedRoles,
  children,
  fallback = null,
  showLoading = false,
}: RoleGuardProps) {
  const { role, isLoading } = useUserRole();

  if (isLoading) {
    return showLoading ? <div>Loading...</div> : null;
  }

  if (!role || !allowedRoles.includes(role)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Convenience components
export function AdminOnly({
  children,
  fallback,
}: {
  children: ReactNode;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard allowedRoles={["admin"]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function SubscriberOnly({
  children,
  fallback,
}: {
  children: ReactNode;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard allowedRoles={["subscriber"]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}
