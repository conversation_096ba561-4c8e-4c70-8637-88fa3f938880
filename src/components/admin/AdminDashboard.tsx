"use client";

import { useEffect, useState } from "react";
import { createClient } from "@/lib/supabase/client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { PLATFORM_PRICING } from "@/lib/constants/pricing";
import { UserLimitsManager } from "./UserLimitsManager";
import AdminPageHistory from "./AdminPageHistory";

interface AdminStats {
  totalUsers: number;
  totalPages: number;
  totalLogs: number;
  totalFeedback: number;
  totalRealCost: number;
  totalPlatformRevenue: number;
  totalMargin: number;
  averageMarginPerPage: number;
}

// Helper functions for safe calculations
const calculatePercentage = (
  numerator?: number,
  denominator?: number
): string => {
  if (!numerator || !denominator || denominator === 0) {
    return "0%";
  }
  return `${((numerator / denominator) * 100).toFixed(1)}%`;
};

const safeDivide = (
  numerator?: number,
  denominator?: number,
  decimals = 1
): string => {
  if (!numerator || !denominator || denominator === 0) {
    return "0";
  }
  return (numerator / denominator).toFixed(decimals);
};

const safeDivideWithCurrency = (
  numerator?: number,
  denominator?: number
): string => {
  if (!numerator || !denominator || denominator === 0) {
    return "$0.00";
  }
  return formatCurrency(numerator / denominator);
};

export function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const abortController = new AbortController();

    const fetchStats = async () => {
      try {
        const supabase = createClient();
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session?.access_token) {
          throw new Error("No authentication token available");
        }

        const response = await fetch("/api/admin/stats", {
          headers: {
            Authorization: `Bearer ${session.access_token}`,
            "Content-Type": "application/json",
          },
          signal: abortController.signal,
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch admin stats: ${response.statusText}`
          );
        }

        const data = await response.json();

        // Only update state if component is still mounted
        if (!abortController.signal.aborted) {
          setStats(data);
        }
      } catch (error) {
        // Don't update state if the request was aborted due to unmounting
        if (error instanceof Error && error.name === "AbortError") {
          return;
        }

        console.error("Error fetching admin stats:", error);
        if (!abortController.signal.aborted) {
          setError(
            error instanceof Error
              ? error.message
              : "Failed to load admin stats"
          );
        }
      } finally {
        if (!abortController.signal.aborted) {
          setIsLoading(false);
        }
      }
    };

    fetchStats();

    // Cleanup function to abort the fetch when component unmounts
    return () => {
      abortController.abort();
    };
  }, []);

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">Loading admin dashboard...</div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-red-500">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">Registered users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pages</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalPages || 0}</div>
            <p className="text-xs text-muted-foreground">Generated pages</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalLogs || 0}</div>
            <p className="text-xs text-muted-foreground">Generation attempts</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Feedback
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.totalFeedback || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              User feedback entries
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Financial Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Real Costs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(stats?.totalRealCost || 0)}
            </div>
            <p className="text-xs text-muted-foreground">Actual API costs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Platform Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(stats?.totalPlatformRevenue || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total charged to users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Margin</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(stats?.totalMargin || 0)}
            </div>
            <p className="text-xs text-muted-foreground">Revenue - Costs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Margin/Page
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(stats?.averageMarginPerPage || 0)}
            </div>
            <p className="text-xs text-muted-foreground">Per page profit</p>
          </CardContent>
        </Card>
      </div>

      {/* User Limits Management */}
      <div className="mb-8">
        <UserLimitsManager />
      </div>

      {/* Page History */}
      <div className="mb-8">
        <AdminPageHistory />
      </div>

      {/* Admin Features */}
      <Card>
        <CardHeader>
          <CardTitle>Admin Features</CardTitle>
          <CardDescription>
            Administrative tools and system management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">Financial Overview</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Margin Rate:</span>
                    <span className="font-medium">
                      {calculatePercentage(
                        stats?.totalMargin,
                        stats?.totalPlatformRevenue
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cost per Page:</span>
                    <span className="font-medium">
                      {safeDivideWithCurrency(
                        stats?.totalRealCost,
                        stats?.totalPages
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Platform Price:</span>
                    <span className="font-medium">
                      {formatCurrency(PLATFORM_PRICING.pricePerPage)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">System Health</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Success Rate:</span>
                    <span className="font-medium">
                      {calculatePercentage(stats?.totalPages, stats?.totalLogs)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg. Attempts/Page:</span>
                    <span className="font-medium">
                      {safeDivide(stats?.totalLogs, stats?.totalPages)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <p>🚧 Additional admin features coming soon:</p>
              <ul className="list-disc list-inside space-y-2 text-sm text-muted-foreground mt-2">
                <li>User management and role assignment</li>
                <li>Detailed cost analytics and trends</li>
                <li>Content moderation tools</li>
                <li>System configuration</li>
                <li>Export and reporting tools</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
