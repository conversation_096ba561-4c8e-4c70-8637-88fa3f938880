"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface UserLimitRow {
  id: string;
  email: string;
  currentLimit: number;
  newLimit: string;
  updating: boolean;
  resetting: boolean;
}

interface ApiUser {
  id: string;
  email: string;
  daily_page_limit: number;
  created_at: string;
}

export function UserLimitsManager() {
  const [users, setUsers] = useState<UserLimitRow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);

      const response = await fetch("/api/admin/users");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch users");
      }

      const userRows: UserLimitRow[] = data.users.map((user: ApiUser) => ({
        id: user.id,
        email: user.email,
        currentLimit: user.daily_page_limit,
        newLimit: "",
        updating: false,
        resetting: false,
      }));

      setUsers(userRows);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load users");
    } finally {
      setLoading(false);
    }
  };

  const updateUserLimit = async (userId: string, newLimit: number) => {
    try {
      // Set updating state for this user
      setUsers((prev) =>
        prev.map((user) =>
          user.id === userId ? { ...user, updating: true } : user
        )
      );

      const response = await fetch("/api/admin/user-limits", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          dailyLimit: newLimit,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update user limit");
      }

      // Update the user's current limit and clear the input
      setUsers((prev) =>
        prev.map((user) =>
          user.id === userId
            ? { ...user, currentLimit: newLimit, newLimit: "", updating: false }
            : user
        )
      );
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to update user limit"
      );
      // Reset updating state on error
      setUsers((prev) =>
        prev.map((user) =>
          user.id === userId ? { ...user, updating: false } : user
        )
      );
    }
  };

  const resetDailyUsage = async (userId: string) => {
    try {
      // Set resetting state for this user
      setUsers((prev) =>
        prev.map((user) =>
          user.id === userId ? { ...user, resetting: true } : user
        )
      );

      const response = await fetch("/api/admin/user-limits", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to reset daily usage");
      }

      // Reset state and show success
      setUsers((prev) =>
        prev.map((user) =>
          user.id === userId ? { ...user, resetting: false } : user
        )
      );

      // Clear any previous errors on success
      setError(null);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to reset daily usage"
      );
      // Reset resetting state on error
      setUsers((prev) =>
        prev.map((user) =>
          user.id === userId ? { ...user, resetting: false } : user
        )
      );
    }
  };

  const handleLimitChange = (userId: string, value: string) => {
    setUsers((prev) =>
      prev.map((user) =>
        user.id === userId ? { ...user, newLimit: value } : user
      )
    );
  };

  const handleUpdate = (userId: string, newLimitStr: string) => {
    const newLimit = parseInt(newLimitStr);
    if (isNaN(newLimit) || newLimit < 0 || newLimit > 1000) {
      setError("Please enter a valid limit between 0 and 1000");
      return;
    }
    setError(null);
    updateUserLimit(userId, newLimit);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>User Daily Limits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2">Loading users...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Daily Limits</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-md">
            {error}
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2 font-medium">User ID</th>
                <th className="text-left p-2 font-medium">Email</th>
                <th className="text-left p-2 font-medium">Daily Limit</th>
                <th className="text-left p-2 font-medium">New Limit</th>
                <th className="text-left p-2 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id} className="border-b hover:bg-gray-50">
                  <td className="p-2">
                    <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded">
                      {user.id.slice(0, 8)}...
                    </span>
                  </td>
                  <td className="p-2">{user.email}</td>
                  <td className="p-2">
                    <span className="font-medium">{user.currentLimit}</span>
                  </td>
                  <td className="p-2">
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        min="0"
                        max="1000"
                        value={user.newLimit}
                        onChange={(e) =>
                          handleLimitChange(user.id, e.target.value)
                        }
                        placeholder="Enter new limit"
                        className="w-32"
                        disabled={user.updating || user.resetting}
                      />
                      <Button
                        size="sm"
                        onClick={() => handleUpdate(user.id, user.newLimit)}
                        disabled={
                          !user.newLimit || user.updating || user.resetting
                        }
                      >
                        {user.updating ? "Updating..." : "Update"}
                      </Button>
                    </div>
                  </td>
                  <td className="p-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => resetDailyUsage(user.id)}
                      disabled={user.updating || user.resetting}
                    >
                      {user.resetting ? "Resetting..." : "Reset Usage"}
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {users.length === 0 && !loading && (
          <div className="text-center py-8 text-muted-foreground">
            No users found
          </div>
        )}
      </CardContent>
    </Card>
  );
}
