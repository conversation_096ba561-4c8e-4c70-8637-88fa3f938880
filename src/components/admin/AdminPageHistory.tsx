"use client";
import { useEffect, useState, useMemo, useCallback } from "react";
import { AdminPageListItemResponse, AdminPageListResponse } from "@/types";
import Link from "next/link";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import {
  ArrowPathIcon,
  TrashIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";
import DeletePageButton from "../dashboard/DeletePageButton";
import { PLATFORM_PRICING } from "@/lib/constants/pricing";

/**
 * Extract model name without provider prefix (e.g., "openai/gpt-4.1" -> "gpt-4.1")
 */
const getModelName = (fullModelName: string): string => {
  const parts = fullModelName.split("/");
  return parts.length > 1 ? parts[1] : fullModelName;
};

/**
 * Format milliseconds into a human-readable time string (e.g. "2m 30s" or "45s")
 */
const formatGenerationTime = (milliseconds: number): string => {
  if (!milliseconds) return "0s";

  const totalSeconds = Math.round(milliseconds / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
};

/**
 * Calculate revenue metrics for a collection of pages
 */
const calculateRevenueMetrics = (pages: AdminPageListItemResponse[]) => {
  const totalRealCost = pages.reduce((sum, p) => sum + (p.cost?.total ?? 0), 0);
  const totalPlatformValue = pages.length * PLATFORM_PRICING.pricePerPage;
  const platformRevenue = totalPlatformValue - totalRealCost;
  const avgRealCostPerPage =
    pages.length === 0 ? 0 : totalRealCost / pages.length;

  return {
    totalRealCost,
    totalPlatformValue,
    platformRevenue,
    avgRealCostPerPage,
    pageCount: pages.length,
    currency: PLATFORM_PRICING.currency,
    pricePerPage: PLATFORM_PRICING.pricePerPage,
  };
};

const getPageTypeLabel = (type: string) => {
  const types: Record<string, string> = {
    "lead-generation": "Lead Generation",
    sales: "Sales",
    product: "Product",
    webinar: "Webinar/Event",
  };
  return types[type] ?? type;
};

// AdminPageSummary component
const AdminPageSummary = ({
  pages,
}: {
  pages: AdminPageListItemResponse[];
}) => {
  const revenueMetrics = useMemo(() => calculateRevenueMetrics(pages), [pages]);
  return (
    <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg shadow-sm">
      <h3 className="text-sm font-semibold text-gray-700 mb-2">
        Admin Cost and Revenue Summary
      </h3>
      <div className="flex flex-wrap gap-x-8 gap-y-2">
        <div>
          <span className="text-xs text-gray-500">Real Total Cost:</span>
          <span className="ml-2 font-medium">
            ${revenueMetrics.totalRealCost.toFixed(5)} {revenueMetrics.currency}
          </span>
        </div>
        <div>
          <span className="text-xs text-gray-500">Platform Revenue:</span>
          <span className="ml-2 font-medium text-green-600">
            ${revenueMetrics.platformRevenue.toFixed(2)}{" "}
            {revenueMetrics.currency}
          </span>
        </div>
        <div>
          <span className="text-xs text-gray-500">Platform Value:</span>
          <span className="ml-2 font-medium">
            ${revenueMetrics.totalPlatformValue.toFixed(2)}{" "}
            {revenueMetrics.currency}
          </span>
        </div>
        <div>
          <span className="text-xs text-gray-500">Total Pages:</span>
          <span className="ml-2 font-medium">{pages.length}</span>
        </div>
        <div>
          <span className="text-xs text-gray-500">
            Avg. Real Cost Per Page:
          </span>
          <span className="ml-2 font-medium">
            ${revenueMetrics.avgRealCostPerPage.toFixed(5)}{" "}
            {revenueMetrics.currency}
          </span>
        </div>
        <div>
          <span className="text-xs text-gray-500">Total Generation Time:</span>
          <span className="ml-2 font-medium">
            {formatGenerationTime(
              pages.reduce((sum, p) => sum + (p.generation_duration_ms ?? 0), 0)
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

// BulkDeleteModal component
const BulkDeleteModal = ({
  show,
  isDeleting,
  selectedPages,
  bulkDeleteError,
  onCancel,
  onConfirm,
}: {
  show: boolean;
  isDeleting: boolean;
  selectedPages: number[];
  bulkDeleteError: string | null;
  onCancel: () => void;
  onConfirm: () => void;
}) => {
  if (!show) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 className="text-lg font-medium mb-4">Confirm Bulk Deletion</h3>
        <p className="mb-4 text-left">
          Are you sure you want to delete {selectedPages.length}{" "}
          {selectedPages.length === 1 ? "page" : "pages"}?
          <span className="block mt-2 text-sm text-gray-500">
            This action cannot be undone.
          </span>
        </p>
        {bulkDeleteError && (
          <div className="bg-red-50 text-red-500 p-3 rounded-md mb-4">
            {bulkDeleteError}
          </div>
        )}
        <div className="flex justify-end gap-3">
          <button
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 cursor-pointer"
            onClick={onCancel}
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center cursor-pointer"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting && (
              <span className="mr-2 animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
            )}
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

// AdminPageTable component
const AdminPageTable = ({
  pages,
  selectedPages,
  allSelected,
  toggleSelectAll,
  togglePageSelection,
  getPageTypeLabel,
  revenueMetrics,
  handlePageDeleted,
}: {
  pages: AdminPageListItemResponse[];
  selectedPages: number[];
  allSelected: boolean;
  toggleSelectAll: () => void;
  togglePageSelection: (id: number) => void;
  getPageTypeLabel: (type: string) => string;
  revenueMetrics: ReturnType<typeof calculateRevenueMetrics>;
  handlePageDeleted: (id: number) => void;
}) => (
  <div className="bg-white shadow-sm rounded-md overflow-hidden">
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-4 py-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 border-gray-300 rounded"
                checked={allSelected}
                onChange={toggleSelectAll}
                aria-label="Select all pages"
              />
            </div>
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Date Created
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            User ID
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider max-w-[150px]">
            Topic
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Status
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Cost
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Model
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Generation Time
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {pages.map((page) => (
          <tr key={page.id} className="hover:bg-gray-50">
            <td className="px-4 py-4 whitespace-nowrap">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 border-gray-300 rounded"
                  checked={selectedPages.includes(page.id)}
                  onChange={() => togglePageSelection(page.id)}
                  aria-label={`Select page ${page.id}`}
                />
              </div>
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {format(new Date(page.created_at), "MMM d, yyyy h:mm a")}
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
              {page.user_id?.slice(0, 8)}...
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 max-w-[150px] truncate">
              {page.config.keywords.slice(0, 2).join(", ")}
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  page.status === "SUCCESS"
                    ? "bg-green-100 text-green-800"
                    : page.status === "FAILED"
                    ? "bg-red-100 text-red-800"
                    : "bg-yellow-100 text-yellow-800"
                }`}
              >
                {page.status}
              </span>
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm">
              {page.cost?.total ? (
                <div>
                  <span className="font-medium text-gray-700">
                    ${page.cost.total.toFixed(5)} {page.cost.currency}
                    {page.generation_attempts &&
                      page.generation_attempts > 1 && (
                        <span className="ml-1 text-xs text-gray-500">
                          ({page.generation_attempts} attempts)
                        </span>
                      )}
                  </span>
                  <div className="text-xs text-green-600">
                    Platform: ${PLATFORM_PRICING.pricePerPage.toFixed(2)}{" "}
                    {revenueMetrics.currency}
                  </div>
                </div>
              ) : (
                <span className="text-gray-400">-</span>
              )}
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm">
              {page.model ? (
                <span className="text-gray-700 font-mono text-xs">
                  {getModelName(page.model)}
                </span>
              ) : (
                <span className="text-gray-400">-</span>
              )}
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm">
              {formatGenerationTime(page.generation_duration_ms ?? 0)}
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div className="flex items-center space-x-3 justify-end">
                <Link
                  href={`/preview/${page.id}`}
                  className="text-primary-600 hover:text-primary-900 p-1 rounded hover:bg-gray-100"
                  aria-label="View page"
                >
                  <EyeIcon className="h-4 w-4" />
                </Link>
                <button
                  onClick={() =>
                    window.open(`/api/preview/${page.id}/download`, "_blank")
                  }
                  className="text-primary-600 hover:text-primary-900 p-1 rounded hover:bg-gray-100 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                  aria-label="Download page"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>
                <DeletePageButton
                  pageId={page.id}
                  pageTitle={page.config.keywords.join(", ")}
                  pageType={getPageTypeLabel(page.config.type)}
                  className="text-red-600 hover:text-red-700"
                  onDeleted={() => handlePageDeleted(page.id)}
                />
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

export default function AdminPageHistory() {
  const router = useRouter();
  const [pages, setPages] = useState<AdminPageListItemResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [total, setTotal] = useState(0);
  const [statusFilter, setStatusFilter] = useState<
    "ALL" | "PENDING" | "SUCCESS" | "FAILED"
  >("ALL");
  const [selectedPages, setSelectedPages] = useState<number[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [bulkDeleteError, setBulkDeleteError] = useState<string | null>(null);

  const fetchPages = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(
        `/api/admin/pages?page=${page}&limit=${limit}&status=${statusFilter}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch pages");
      }
      const data: AdminPageListResponse = await response.json();
      setPages(data.items);
      setTotal(data.total);
    } catch (err) {
      setError("Failed to load page history");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, [page, limit, statusFilter]);

  useEffect(() => {
    fetchPages();
  }, [fetchPages]);

  const handleNextPage = useCallback(() => {
    if (page * limit < total) {
      setPage(page + 1);
    }
  }, [page, limit, total]);

  const handlePreviousPage = useCallback(() => {
    if (page > 1) {
      setPage(page - 1);
    }
  }, [page]);

  const handleStatusChange = useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      setStatusFilter(
        e.target.value as "ALL" | "PENDING" | "SUCCESS" | "FAILED"
      );
      setPage(1);
    },
    []
  );

  const paginationText = useMemo(() => {
    if (total === 0) return "No results";
    const start = (page - 1) * limit + 1;
    const end = Math.min(page * limit, total);
    return `Showing ${start} to ${end} of ${total} results`;
  }, [page, limit, total]);

  const revenueMetrics = useMemo(() => calculateRevenueMetrics(pages), [pages]);

  const handlePageDeleted = useCallback((deletedId: number) => {
    setPages((currentPages) => currentPages.filter((p) => p.id !== deletedId));
    setTotal((prev) => prev - 1);
    setSelectedPages((prev) => prev.filter((id) => id !== deletedId));
  }, []);

  const togglePageSelection = useCallback((pageId: number) => {
    setSelectedPages((prev) =>
      prev.includes(pageId)
        ? prev.filter((id) => id !== pageId)
        : [...prev, pageId]
    );
  }, []);

  const toggleSelectAll = useCallback(() => {
    setSelectedPages((prev) =>
      prev.length === pages.length ? [] : pages.map((p) => p.id)
    );
  }, [pages]);

  const allSelected = pages.length > 0 && selectedPages.length === pages.length;
  const hasSelection = selectedPages.length > 0;

  const handleBulkDelete = useCallback(() => {
    if (selectedPages.length === 0) return;
    setShowBulkDeleteConfirm(true);
  }, [selectedPages]);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedPages.length === 0) return;
    setIsDeleting(true);
    setBulkDeleteError(null);
    try {
      const results = await Promise.allSettled(
        selectedPages.map((pageId) =>
          fetch(`/api/preview/${pageId}`, {
            method: "DELETE",
            credentials: "include",
            headers: {
              "Content-Type": "application/json",
            },
          }).then(async (response) => {
            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(
                errorData.error ?? `Failed to delete page ${pageId}`
              );
            }
            return response;
          })
        )
      );
      const successCount = results.filter(
        (result) => result.status === "fulfilled"
      ).length;
      if (successCount > 0) {
        setPages((currentPages) =>
          currentPages.filter((p) => !selectedPages.includes(p.id))
        );
        setTotal((prev) => prev - successCount);
        setSelectedPages([]);
        if (successCount === selectedPages.length) {
          setShowBulkDeleteConfirm(false);
          router.refresh();
        }
      }
      const failedCount = selectedPages.length - successCount;
      if (failedCount > 0) {
        const errorMessages = results
          .filter((result) => result.status === "rejected")
          .map((result) =>
            result.status === "rejected" ? result.reason.message : ""
          )
          .filter(Boolean)
          .slice(0, 3);
        const errorMessage =
          errorMessages.length > 0
            ? `Failed to delete ${failedCount} page(s): ${errorMessages.join(
                ", "
              )}${errorMessages.length < failedCount ? "..." : ""}`
            : `Failed to delete ${failedCount} page(s). Please try again.`;
        setBulkDeleteError(errorMessage);
      } else {
        setShowBulkDeleteConfirm(false);
      }
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "An unknown error occurred";
      setBulkDeleteError(message);
      console.error("Bulk delete error:", err);
    } finally {
      setIsDeleting(false);
    }
  }, [selectedPages, router]);

  return (
    <div className="w-full mb-8" data-testid="admin-page-history">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-3">
          <h2 className="text-2xl font-bold">All Generated Pages (Admin)</h2>
          <button
            onClick={fetchPages}
            className="p-1 rounded-md hover:bg-gray-100"
            title="Refresh"
            aria-label="Refresh list"
            disabled={isLoading}
          >
            <ArrowPathIcon
              className={`w-5 h-5 text-gray-500 ${
                isLoading ? "animate-spin" : ""
              }`}
            />
          </button>
        </div>
        <div className="flex items-center gap-2">
          <label
            htmlFor="status-filter"
            className="text-sm text-muted-foreground"
          >
            Filter:
          </label>
          <select
            id="status-filter"
            value={statusFilter}
            onChange={handleStatusChange}
            className="border rounded-md px-2 py-1 text-sm"
          >
            <option value="ALL">All</option>
            <option value="PENDING">Pending</option>
            <option value="SUCCESS">Successful</option>
            <option value="FAILED">Failed</option>
          </select>
        </div>
      </div>
      {pages.length > 0 && <AdminPageSummary pages={pages} />}
      {pages.length > 0 && (
        <div
          className={`flex items-center gap-2 my-2 transition-opacity ${
            hasSelection ? "opacity-100" : "opacity-0"
          }`}
        >
          <span className="text-sm text-gray-500">
            {selectedPages.length}{" "}
            {selectedPages.length === 1 ? "page" : "pages"} selected
          </span>
          <button
            onClick={handleBulkDelete}
            disabled={!hasSelection}
            className={`inline-flex items-center px-3 py-1.5 text-xs rounded-md text-white ${
              hasSelection
                ? "bg-red-600 hover:bg-red-700"
                : "bg-gray-300 cursor-not-allowed"
            }`}
          >
            <TrashIcon className="h-3 w-3 mr-1" />
            Delete Selected
          </button>
        </div>
      )}
      {isLoading ? (
        <div className="flex justify-center py-8">
          <span className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400" />
        </div>
      ) : error ? (
        <div className="bg-red-50 text-red-500 p-4 rounded-md text-center">
          {error}
        </div>
      ) : pages.length === 0 ? (
        <div className="bg-muted p-8 rounded-md text-center">
          <p className="text-muted-foreground">No pages found.</p>
        </div>
      ) : (
        <>
          <AdminPageTable
            pages={pages}
            selectedPages={selectedPages}
            allSelected={allSelected}
            toggleSelectAll={toggleSelectAll}
            togglePageSelection={togglePageSelection}
            getPageTypeLabel={getPageTypeLabel}
            revenueMetrics={revenueMetrics}
            handlePageDeleted={handlePageDeleted}
          />
          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-gray-500">{paginationText}</div>
            <div className="flex gap-2">
              <button
                onClick={handlePreviousPage}
                disabled={page === 1}
                className={`px-3 py-1 rounded border ${
                  page === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                }`}
              >
                Previous
              </button>
              <button
                onClick={handleNextPage}
                disabled={page * limit >= total}
                className={`px-3 py-1 rounded border ${
                  page * limit >= total
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
                }`}
              >
                Next
              </button>
            </div>
          </div>
        </>
      )}
      <BulkDeleteModal
        show={showBulkDeleteConfirm}
        isDeleting={isDeleting}
        selectedPages={selectedPages}
        bulkDeleteError={bulkDeleteError}
        onCancel={() => setShowBulkDeleteConfirm(false)}
        onConfirm={handleConfirmBulkDelete}
      />
    </div>
  );
}
