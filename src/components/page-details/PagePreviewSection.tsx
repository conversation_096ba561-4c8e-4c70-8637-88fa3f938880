"use client";

import { useState, useMemo, useCallback, useEffect } from "react";
import {
  ResponsivePreview,
  ViewportControls,
} from "@/components/ui/ResponsivePreview";
import { FullscreenPreview } from "@/components/ui/FullscreenPreview";
import { formatHtml } from "@/services/page-generator/html-processor";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneLight } from "react-syntax-highlighter/dist/esm/styles/prism";
import { ClipboardDocumentIcon } from "@heroicons/react/24/outline";
import { ImprovedEditablePreview } from "@/components/editor/ImprovedEditablePreview";
import { PageConfig } from "@/types";
import { toast } from "sonner";

interface PagePreviewSectionProps {
  htmlContent: string;
  pageId?: number;
  pageConfig?: PageConfig;
}

export function PagePreviewSection({
  htmlContent,
  pageId,
  pageConfig,
}: PagePreviewSectionProps) {
  const [activeTab, setActiveTab] = useState("preview");
  const [activeViewport, setActiveViewport] = useState<
    "desktop" | "tablet" | "mobile"
  >("desktop");
  const [orientation, setOrientation] = useState<"portrait" | "landscape">(
    "portrait"
  );
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedContent, setEditedContent] = useState(htmlContent);
  const [hasChanges, setHasChanges] = useState(false);
  // Store original content for proper revert functionality
  const [originalContent, setOriginalContent] = useState(htmlContent);

  // Update original content when htmlContent prop changes
  useEffect(() => {
    setOriginalContent(htmlContent);
    setEditedContent(htmlContent);
  }, [htmlContent]);

  // Memoize formatted HTML to avoid recomputation on every render
  const formattedHtml = useMemo(() => {
    return htmlContent ? formatHtml(htmlContent) : "";
  }, [htmlContent]);

  const handleCopyCode = useCallback(async () => {
    if (!formattedHtml) return;

    try {
      await navigator.clipboard.writeText(formattedHtml);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error("Failed to copy code:", err);
    }
  }, [formattedHtml]);

  const handleSave = async () => {
    if (!pageId) {
      toast.error("Cannot save: Page ID not available");
      return;
    }

    try {
      // For revision system: Create new page with parent_page_id instead of updating existing
      const response = await fetch(`/api/pages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          config: pageConfig,
          html_content: editedContent,
          parent_page_id: pageId, // Set current page as parent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save changes");
      }

      const newRevision = await response.json();
      toast.success("New revision saved successfully");

      // Redirect to the new revision
      window.location.href = `/preview/${newRevision.id}`;
    } catch (error) {
      console.error("Save error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to save changes"
      );
    }
  };

  const handleCancel = () => {
    setEditedContent(originalContent);
    setHasChanges(false);
    setIsEditMode(false);
  };

  const handleRevertToOriginal = async () => {
    if (!pageId) {
      toast.error("Cannot revert: Page ID not available");
      return;
    }

    try {
      // Find the original page (parent_page_id IS NULL)
      // First get current page to find its parent or itself if it's the original
      const currentPageResponse = await fetch(`/api/preview/${pageId}`);
      if (!currentPageResponse.ok) {
        throw new Error("Failed to fetch current page");
      }

      const currentPage = await currentPageResponse.json();
      const originalPageId = currentPage.parent_page_id ?? pageId;

      // Fetch the original page content
      const response = await fetch(`/api/preview/${originalPageId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch original content");
      }

      const originalPage = await response.json();

      if (originalPage.html_content) {
        setEditedContent(originalPage.html_content);
        setHasChanges(true);
        toast.success("Reverted to original version");
      } else {
        throw new Error("No original content found");
      }
    } catch (error) {
      console.error("Revert error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to revert to original"
      );
    }
  };

  const handleContentChange = (newContent: string) => {
    setEditedContent(newContent);
  };

  const handleEditModeToggle = () => {
    setIsEditMode(!isEditMode);
    if (!isEditMode) {
      setEditedContent(originalContent);
    }
  };

  // Reset to portrait when switching between tablet and mobile
  const handleViewportChange = (
    newViewport: "desktop" | "tablet" | "mobile"
  ) => {
    setActiveViewport(newViewport);
    if (newViewport === "tablet" || newViewport === "mobile") {
      setOrientation("portrait");
    }
  };

  // Dynamic height based on viewport and orientation
  const getContainerHeight = () => {
    switch (activeViewport) {
      case "tablet":
        return orientation === "portrait" ? "h-[1024px]" : "h-[768px]";
      case "mobile":
        return orientation === "portrait" ? "h-[667px]" : "h-[375px]";
      default:
        return "h-[1024px]"; // desktop
    }
  };

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <div className="border-b px-6 py-4">
        <div className="flex justify-between items-center">
          <div className="flex" role="tablist" aria-label="Page view options">
            <button
              role="tab"
              aria-selected={activeTab === "preview"}
              aria-controls="preview-panel"
              tabIndex={activeTab === "preview" ? 0 : -1}
              className={`px-4 py-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer ${
                activeTab === "preview"
                  ? "border-b-2 border-primary font-bold"
                  : "text-muted-foreground"
              }`}
              onClick={() => setActiveTab("preview")}
            >
              Preview
            </button>
            <button
              role="tab"
              aria-selected={activeTab === "code"}
              aria-controls="code-panel"
              tabIndex={activeTab === "code" ? 0 : -1}
              className={`px-4 py-2 cursor-pointer hover:cursor-pointer focus:cursor-pointer ${
                activeTab === "code"
                  ? "border-b-2 border-primary font-bold"
                  : "text-muted-foreground"
              }`}
              onClick={() => setActiveTab("code")}
            >
              Code
            </button>
          </div>
          <div className="flex items-center gap-2">
            {activeTab === "preview" && !isEditMode && pageId && pageConfig && (
              <button
                onClick={handleEditModeToggle}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
              >
                ✏️ Edit Page
              </button>
            )}
            {activeTab === "preview" && isEditMode && (
              <>
                <button
                  onClick={handleSave}
                  disabled={!hasChanges}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  💾 Save
                </button>
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  ❌ Cancel
                </button>
                <button
                  onClick={handleRevertToOriginal}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  ↩️ Revert
                </button>
              </>
            )}
            {activeTab === "preview" && !isEditMode && (
              <ViewportControls
                activeViewport={activeViewport}
                onViewportChange={handleViewportChange}
                orientation={orientation}
                onOrientationChange={setOrientation}
                onFullscreen={() => setIsFullscreen(true)}
              />
            )}
          </div>
        </div>
      </div>
      <div className={`${getContainerHeight()} overflow-hidden`}>
        {htmlContent ? (
          activeTab === "preview" ? (
            isEditMode && pageId && pageConfig ? (
              <ImprovedEditablePreview
                htmlContent={editedContent}
                isEditMode={isEditMode}
                activeViewport={activeViewport}
                orientation={orientation}
                onContentChange={handleContentChange}
                onHasChanges={setHasChanges}
              />
            ) : (
              <ResponsivePreview
                html={htmlContent}
                activeViewport={activeViewport}
                orientation={orientation}
              />
            )
          ) : (
            <div className="h-full overflow-auto relative">
              <button
                onClick={handleCopyCode}
                className="absolute top-2 right-2 z-10 p-2 bg-white/90 hover:bg-white border border-gray-200 rounded-md shadow-sm transition-colors cursor-pointer"
                title={copySuccess ? "Copied!" : "Copy code"}
              >
                <ClipboardDocumentIcon className="w-4 h-4 text-gray-600" />
              </button>
              {copySuccess && (
                <div className="absolute top-2 right-16 z-10 px-3 py-1 bg-green-500 text-white text-sm rounded-md shadow-sm">
                  Copied!
                </div>
              )}
              <SyntaxHighlighter
                language="html"
                style={oneLight}
                customStyle={{
                  margin: 0,
                  height: "100%",
                  fontSize: "12px",
                  lineHeight: "1.4",
                }}
                showLineNumbers={true}
                wrapLines={true}
                wrapLongLines={true}
              >
                {formattedHtml}
              </SyntaxHighlighter>
            </div>
          )
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <p className="text-muted-foreground">No content available</p>
          </div>
        )}
      </div>

      <FullscreenPreview
        html={htmlContent}
        isOpen={isFullscreen}
        onClose={() => setIsFullscreen(false)}
        title="Landing Page"
      />
    </div>
  );
}
