"use client";

import {
  ComputerDesktopIcon,
  DeviceTabletIcon,
  DevicePhoneMobileIcon,
  ArrowPathIcon,
  ArrowsPointingOutIcon,
} from "@heroicons/react/24/outline";

type ViewportType = "desktop" | "tablet" | "mobile";
type OrientationType = "portrait" | "landscape";

interface ResponsivePreviewProps {
  html: string;
  title?: string;
  className?: string;
  activeViewport?: ViewportType;
  orientation?: OrientationType;
}

const viewportConfig = {
  desktop: {
    icon: ComputerDesktopIcon,
    label: "Desktop",
    portrait: {
      width: "100%",
      height: "100%",
      containerClass: "w-full h-full",
    },
    landscape: {
      width: "100%",
      height: "100%",
      containerClass: "w-full h-full",
    },
  },
  tablet: {
    icon: DeviceTabletIcon,
    label: "Tablet",
    portrait: {
      width: "768px",
      height: "1024px",
      containerClass: "w-[768px] h-[1024px] mx-auto",
    },
    landscape: {
      width: "1024px",
      height: "768px",
      containerClass: "w-[1024px] h-[768px] mx-auto",
    },
  },
  mobile: {
    icon: DevicePhoneMobileIcon,
    label: "Mobile",
    portrait: {
      width: "375px",
      height: "667px",
      containerClass: "w-[375px] h-[667px] mx-auto",
    },
    landscape: {
      width: "667px",
      height: "375px",
      containerClass: "w-[667px] h-[375px] mx-auto",
    },
  },
};

export function ViewportControls({
  activeViewport,
  onViewportChange,
  orientation,
  onOrientationChange,
  onFullscreen,
}: {
  activeViewport: ViewportType;
  onViewportChange: (viewport: ViewportType) => void;
  orientation: OrientationType;
  onOrientationChange: (orientation: OrientationType) => void;
  onFullscreen?: () => void;
}) {
  const showOrientationToggle =
    activeViewport === "tablet" || activeViewport === "mobile";

  return (
    <div className="flex items-center gap-1 p-1 bg-gray-50 rounded-lg">
      {onFullscreen && (
        <>
          <button
            onClick={onFullscreen}
            className="flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors text-muted-foreground hover:text-foreground hover:bg-white cursor-pointer hover:cursor-pointer focus:cursor-pointer"
            aria-label="Open fullscreen preview"
          >
            <ArrowsPointingOutIcon className="w-4 h-4" />
            <span className="hidden sm:inline">Fullscreen</span>
          </button>
          <div className="w-px h-6 bg-gray-300 mx-1" />
        </>
      )}

      {(Object.keys(viewportConfig) as ViewportType[]).map((viewport) => {
        const config = viewportConfig[viewport];
        const Icon = config.icon;
        const isActive = activeViewport === viewport;

        return (
          <button
            key={viewport}
            onClick={() => onViewportChange(viewport)}
            className={`
              flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors cursor-pointer hover:cursor-pointer focus:cursor-pointer
              ${
                isActive
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-white"
              }
            `}
            aria-label={`Switch to ${config.label} view`}
          >
            <Icon className="w-4 h-4" />
            <span className="hidden sm:inline">{config.label}</span>
          </button>
        );
      })}

      {showOrientationToggle && (
        <>
          <div className="w-px h-6 bg-gray-300 mx-1" />
          <button
            onClick={() =>
              onOrientationChange(
                orientation === "portrait" ? "landscape" : "portrait"
              )
            }
            className="flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors text-muted-foreground hover:text-foreground hover:bg-white cursor-pointer hover:cursor-pointer focus:cursor-pointer"
            aria-label={`Switch to ${
              orientation === "portrait" ? "landscape" : "portrait"
            } orientation`}
          >
            <ArrowPathIcon className="w-4 h-4" />
            <span className="hidden sm:inline">
              {orientation === "portrait" ? "Landscape" : "Portrait"}
            </span>
          </button>
        </>
      )}
    </div>
  );
}

export function ResponsivePreview({
  html,
  title = "Landing Page Preview",
  className = "",
  activeViewport = "desktop",
  orientation = "portrait",
}: ResponsivePreviewProps) {
  const currentConfig = viewportConfig[activeViewport][orientation];

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Preview Container */}
      <div className="flex-1 overflow-auto bg-white min-h-[500px]">
        <div className="w-full h-full flex items-center justify-center">
          <div className={currentConfig.containerClass}>
            <iframe
              srcDoc={html}
              title={title}
              className="w-full h-full"
              sandbox="allow-scripts allow-same-origin"
              style={{
                maxWidth: currentConfig.width,
                maxHeight: currentConfig.height,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
