"use client";

import { useEffect, useRef } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface FullscreenPreviewProps {
  html: string;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
}

export function FullscreenPreview({
  html,
  isOpen,
  onClose,
  title = "Landing Page Preview",
}: FullscreenPreviewProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Handle ESC key to close
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    const handleIframeLoad = () => {
      const iframe = iframeRef.current;
      if (iframe?.contentDocument) {
        // Add ESC listener to iframe content
        iframe.contentDocument.addEventListener("keydown", handleEsc);
      }
    };

    if (isOpen) {
      // Add listener to parent document
      document.addEventListener("keydown", handleEsc);

      // Add listener to iframe when it loads
      const iframe = iframeRef.current;
      if (iframe) {
        iframe.addEventListener("load", handleIframeLoad);
        // If iframe is already loaded, add listener immediately
        if (iframe.contentDocument) {
          iframe.contentDocument.addEventListener("keydown", handleEsc);
        }
      }

      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      // Clean up parent document listener
      document.removeEventListener("keydown", handleEsc);

      // Clean up iframe listeners
      const iframe = iframeRef.current;
      if (iframe) {
        iframe.removeEventListener("load", handleIframeLoad);
        if (iframe.contentDocument) {
          iframe.contentDocument.removeEventListener("keydown", handleEsc);
        }
      }

      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-white">
      <button
        onClick={onClose}
        className="fixed left-[18px] top-1/2 -translate-y-1/2 z-20 flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-t-lg shadow-lg text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 rotate-90 cursor-pointer hover:cursor-pointer focus:cursor-pointer"
        style={{ transformOrigin: "left center" }}
        aria-label="Close fullscreen preview"
      >
        <XMarkIcon className="w-4 h-4" />
        <span>Close preview</span>
      </button>

      {/* Fullscreen iframe */}
      <div className="w-full h-full">
        <iframe
          ref={iframeRef}
          srcDoc={html}
          title={title}
          className="w-full h-full border-none"
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
        />
      </div>
    </div>
  );
}
