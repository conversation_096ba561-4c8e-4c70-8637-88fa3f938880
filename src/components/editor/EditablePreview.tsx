"use client";

import { useRef, useEffect, useCallback } from "react";
import { useSimpleTextEditor } from "@/hooks/useSimpleTextEditor";

interface EditablePreviewProps {
  htmlContent: string;
  // pageId: number; // Will be used for image replacement
  // pageConfig: PageConfig; // Will be used for Unsplash search
  isEditMode: boolean;
  activeViewport: "desktop" | "tablet" | "mobile";
  orientation: "portrait" | "landscape";
  onContentChange?: (html: string) => void;
  onHasChanges?: (hasChanges: boolean) => void;
}

export function EditablePreview({
  htmlContent,
  // pageId,
  // pageConfig,
  isEditMode,
  activeViewport,
  orientation,
  onContentChange,
  onHasChanges,
}: EditablePreviewProps) {
  const containerRef = useRef<HTMLElement | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const originalHtmlRef = useRef<string>(htmlContent);
  const isEditingRef = useRef<boolean>(false);
  const iframeLoadedRef = useRef<boolean>(false);

  // Custom content change handler that preserves full HTML structure
  const handleContentChangeWithStructure = useCallback(
    (bodyHtml: string) => {
      if (!onContentChange || !originalHtmlRef.current) return;

      // Set flag to prevent iframe reload during editing
      isEditingRef.current = true;

      // Store currently focused element info before content change
      const iframe = iframeRef.current;
      let focusedElementInfo: { selector?: string; textContent?: string } = {};

      if (iframe?.contentDocument) {
        const activeElement = iframe.contentDocument
          .activeElement as HTMLElement;
        if (activeElement && activeElement !== iframe.contentDocument.body) {
          // Create a selector to find the element later
          const tagName = activeElement.tagName.toLowerCase();
          const textContent = activeElement.textContent?.trim();
          focusedElementInfo = {
            selector: tagName,
            textContent: textContent?.substring(0, 50), // First 50 chars for matching
          };
        }
      }

      // Parse original HTML to extract head content
      const parser = new DOMParser();
      const originalDoc = parser.parseFromString(
        originalHtmlRef.current,
        "text/html"
      );
      const headContent = originalDoc.head.innerHTML;

      // Reconstruct full HTML with preserved head and updated body
      const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
${headContent}
</head>
<body>
${bodyHtml}
</body>
</html>`;

      onContentChange(fullHtml);

      // Attempt to restore focus after a short delay
      if (focusedElementInfo.selector && focusedElementInfo.textContent) {
        setTimeout(() => {
          if (iframe?.contentDocument) {
            const elements = iframe.contentDocument.querySelectorAll(
              focusedElementInfo.selector!
            );
            for (const element of elements) {
              const htmlElement = element as HTMLElement;
              if (
                htmlElement.textContent
                  ?.trim()
                  .startsWith(focusedElementInfo.textContent!)
              ) {
                htmlElement.focus();
                break;
              }
            }
          }
        }, 150);
      }
    },
    [onContentChange]
  );

  const { hasChanges, makeTextEditable, disableTextEditing } =
    useSimpleTextEditor({
      containerRef,
      onContentChange: handleContentChangeWithStructure,
    });

  // Update original HTML reference when content changes
  useEffect(() => {
    // Only update if content actually changed and we're not currently editing
    if (originalHtmlRef.current !== htmlContent && !isEditingRef.current) {
      originalHtmlRef.current = htmlContent;
      iframeLoadedRef.current = false; // Force reload iframe
    }
    // Reset editing flag after content update with delay to prevent immediate re-triggering
    setTimeout(() => {
      isEditingRef.current = false;
    }, 200);
  }, [htmlContent]);

  // Update parent component when hasChanges state changes
  useEffect(() => {
    if (onHasChanges) {
      onHasChanges(hasChanges);
    }
  }, [hasChanges, onHasChanges]);

  // Handle edit mode changes
  useEffect(() => {
    if (isEditMode) {
      // Longer delay to ensure iframe content is fully loaded and stable
      const timeoutId = setTimeout(() => {
        makeTextEditable();
      }, 300);

      return () => clearTimeout(timeoutId);
    } else {
      disableTextEditing();
      // Reset editing flag when exiting edit mode
      isEditingRef.current = false;
    }
  }, [isEditMode, makeTextEditable, disableTextEditing]);

  // Get responsive dimensions
  const getIframeDimensions = () => {
    switch (activeViewport) {
      case "tablet":
        return orientation === "portrait"
          ? { width: 768, height: 1024 }
          : { width: 1024, height: 768 };
      case "mobile":
        return orientation === "portrait"
          ? { width: 375, height: 667 }
          : { width: 667, height: 375 };
      default:
        return { width: 1200, height: 800 }; // desktop
    }
  };

  const { width, height } = getIframeDimensions();

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return;

    const iframeDoc = iframe.contentDocument;

    // Don't reload iframe content if currently editing and already loaded
    if (isEditingRef.current && iframeLoadedRef.current) {
      return;
    }

    // Write HTML content to iframe only if not editing
    iframeDoc.open();
    iframeDoc.write(htmlContent);
    iframeDoc.close();

    // Mark as loaded
    iframeLoadedRef.current = true;

    // Add edit mode class if needed
    if (isEditMode) {
      iframeDoc.body.classList.add("edit-mode");
      // Set container ref to iframe body for the hook
      if (containerRef.current !== iframeDoc.body) {
        containerRef.current = iframeDoc.body;
      }
      // Enable editing after content is loaded with longer delay for stability
      setTimeout(() => {
        if (isEditMode && !isEditingRef.current) {
          makeTextEditable();
        }
      }, 150);
    } else {
      iframeDoc.body.classList.remove("edit-mode");
    }

    // Add custom styles for better editing experience
    const style = iframeDoc.createElement("style");
    style.textContent = `
      body { margin: 0; padding: 0; }
      * { box-sizing: border-box; }
      .editable-text:hover {
        background-color: rgba(59, 130, 246, 0.05);
        outline: 1px dashed rgba(59, 130, 246, 0.3);
      }
      .editable-text:focus {
        background-color: rgba(59, 130, 246, 0.1);
        outline: 2px solid #3b82f6;
      }
    `;
    iframeDoc.head.appendChild(style);
  }, [htmlContent, isEditMode, makeTextEditable]);

  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-100">
      <div
        className="bg-white shadow-lg"
        style={{
          width: `${width}px`,
          height: `${height}px`,
          maxWidth: "100%",
          maxHeight: "100%",
          transform:
            width > window.innerWidth * 0.9
              ? `scale(${(window.innerWidth * 0.9) / width})`
              : "none",
          transformOrigin: "top left",
        }}
      >
        <iframe
          ref={iframeRef}
          className="w-full h-full border-0"
          title="Page Preview"
          onLoad={handleIframeLoad}
          sandbox="allow-same-origin allow-scripts"
        />
      </div>
    </div>
  );
}
