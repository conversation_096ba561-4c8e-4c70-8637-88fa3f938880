"use client";

import { useRef, useEffect, useCallback, useState } from "react";

interface DirectEditablePreviewProps {
  htmlContent: string;
  isEditMode: boolean;
  activeViewport: "desktop" | "tablet" | "mobile";
  orientation: "portrait" | "landscape";
  onContentChange?: (html: string) => void;
  onHasChanges?: (hasChanges: boolean) => void;
}

export function DirectEditablePreview({
  htmlContent,
  isEditMode,
  activeViewport,
  orientation,
  onContentChange,
  onHasChanges,
}: DirectEditablePreviewProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const originalContentRef = useRef<string>("");

  // Extract body content from full HTML
  const extractBodyContent = useCallback((html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.body.innerHTML;
  }, []);

  // Extract head content from full HTML
  const extractHeadContent = useCallback((html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.head.innerHTML;
  }, []);

  // Extract and inject styles from head
  const injectStyles = useCallback((html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");

    // Remove existing injected styles
    const existingStyles = document.querySelectorAll(
      "[data-direct-preview-styles]"
    );
    existingStyles.forEach((style) => style.remove());

    // Extract all style elements and link elements for CSS
    const styleElements = doc.head.querySelectorAll(
      'style, link[rel="stylesheet"]'
    );

    styleElements.forEach((element, index) => {
      if (element.tagName === "STYLE") {
        // Inject style content directly
        const styleEl = document.createElement("style");
        styleEl.textContent = element.textContent;
        styleEl.setAttribute("data-direct-preview-styles", `style-${index}`);
        document.head.appendChild(styleEl);
      } else if (element.tagName === "LINK") {
        // Clone link elements for external CSS
        const linkEl = element.cloneNode(true) as HTMLLinkElement;
        linkEl.setAttribute("data-direct-preview-styles", `link-${index}`);
        document.head.appendChild(linkEl);
      }
    });
  }, []);

  // Reconstruct full HTML from body content
  const reconstructFullHtml = useCallback(
    (bodyContent: string) => {
      const headContent = extractHeadContent(
        originalContentRef.current || htmlContent
      );
      return `<!DOCTYPE html>
<html lang="en">
<head>
${headContent}
</head>
<body>
${bodyContent}
</body>
</html>`;
    },
    [htmlContent, extractHeadContent]
  );

  // Get responsive dimensions
  const getContainerDimensions = () => {
    switch (activeViewport) {
      case "tablet":
        return orientation === "portrait"
          ? { width: 768, height: 1024 }
          : { width: 1024, height: 768 };
      case "mobile":
        return orientation === "portrait"
          ? { width: 375, height: 667 }
          : { width: 667, height: 375 };
      default:
        return { width: "100%", height: "100%" }; // desktop
    }
  };

  // Handle content changes - only update parent state, don't re-render
  const handleContentChange = useCallback(() => {
    if (!contentRef.current || !onContentChange || !isEditing) return;

    const bodyContent = contentRef.current.innerHTML;
    const fullHtml = reconstructFullHtml(bodyContent);

    setHasChanges(true);
    onContentChange(fullHtml);
  }, [onContentChange, reconstructFullHtml, isEditing]);

  // Make text elements editable
  const makeTextEditable = useCallback(() => {
    if (!contentRef.current) return;

    const textSelectors =
      'h1, h2, h3, h4, h5, h6, p, li, span:not([class*="icon"]):not([class*="sr-"]), div:not([class*="container"]):not([class*="wrapper"]):not([class*="flex"]):not([class*="grid"])';
    const textElements = contentRef.current.querySelectorAll(textSelectors);

    textElements.forEach((element) => {
      const htmlElement = element as HTMLElement;

      if (
        htmlElement.textContent?.trim() &&
        !htmlElement.hasAttribute("contenteditable")
      ) {
        htmlElement.setAttribute("contenteditable", "true");
        htmlElement.classList.add("editable-text");

        // Add event listeners - only on blur to prevent constant updates
        htmlElement.addEventListener("blur", handleContentChange);

        // Add visual feedback classes
        htmlElement.classList.add(
          "hover:bg-blue-50",
          "hover:outline",
          "hover:outline-1",
          "hover:outline-dashed",
          "hover:outline-blue-300",
          "focus:bg-blue-100",
          "focus:outline",
          "focus:outline-2",
          "focus:outline-blue-500",
          "transition-all",
          "duration-200"
        );
      }
    });
  }, [handleContentChange]);

  // Disable text editing
  const disableTextEditing = useCallback(() => {
    if (!contentRef.current) return;

    const editableElements =
      contentRef.current.querySelectorAll(".editable-text");
    editableElements.forEach((element) => {
      const htmlElement = element as HTMLElement;

      htmlElement.removeAttribute("contenteditable");
      htmlElement.classList.remove("editable-text");
      htmlElement.removeEventListener("blur", handleContentChange);

      // Remove visual feedback classes
      htmlElement.classList.remove(
        "hover:bg-blue-50",
        "hover:outline",
        "hover:outline-1",
        "hover:outline-dashed",
        "hover:outline-blue-300",
        "focus:bg-blue-100",
        "focus:outline",
        "focus:outline-2",
        "focus:outline-blue-500",
        "transition-all",
        "duration-200"
      );
    });
  }, [handleContentChange]);

  // Update content when htmlContent changes (but not during editing)
  useEffect(() => {
    if (!isEditing && contentRef.current) {
      const bodyContent = extractBodyContent(htmlContent);
      contentRef.current.innerHTML = bodyContent;
      originalContentRef.current = htmlContent;

      // Inject styles from head into document
      injectStyles(htmlContent);
    }
  }, [htmlContent, isEditing, extractBodyContent, injectStyles]);

  // Handle edit mode changes
  useEffect(() => {
    if (isEditMode) {
      setIsEditing(true);
      // Small delay to ensure content is rendered
      setTimeout(() => {
        makeTextEditable();
      }, 100);
    } else {
      disableTextEditing();
      setIsEditing(false);
      setHasChanges(false);
    }
  }, [isEditMode, makeTextEditable, disableTextEditing]);

  // Update parent component when hasChanges state changes
  useEffect(() => {
    if (onHasChanges) {
      onHasChanges(hasChanges);
    }
  }, [hasChanges, onHasChanges]);

  // Cleanup injected styles on unmount
  useEffect(() => {
    return () => {
      const existingStyles = document.querySelectorAll(
        "[data-direct-preview-styles]"
      );
      existingStyles.forEach((style) => style.remove());
    };
  }, []);

  const { width, height } = getContainerDimensions();

  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-100">
      <div
        className="bg-white shadow-lg overflow-auto"
        style={{
          width: typeof width === "number" ? `${width}px` : width,
          height: typeof height === "number" ? `${height}px` : height,
          maxWidth: "100%",
          maxHeight: "100%",
          transform:
            typeof width === "number" && width > window.innerWidth * 0.9
              ? `scale(${(window.innerWidth * 0.9) / width})`
              : "none",
          transformOrigin: "top left",
        }}
      >
        <div
          ref={contentRef}
          className="w-full h-full"
          dangerouslySetInnerHTML={{ __html: extractBodyContent(htmlContent) }}
        />
      </div>
    </div>
  );
}
