"use client";

import { useRef, useEffect, useCallback, useState } from "react";

interface DirectEditablePreviewProps {
  htmlContent: string;
  isEditMode: boolean;
  activeViewport: "desktop" | "tablet" | "mobile";
  orientation: "portrait" | "landscape";
  onContentChange?: (html: string) => void;
  onHasChanges?: (hasChanges: boolean) => void;
}

export function DirectEditablePreview({
  htmlContent,
  isEditMode,
  activeViewport,
  orientation,
  onContentChange,
  onHasChanges,
}: DirectEditablePreviewProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const originalContentRef = useRef<string>("");

  // Extract body content from full HTML
  const extractBodyContent = useCallback((html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.body.innerHTML;
  }, []);

  // Extract head content from full HTML
  const extractHeadContent = useCallback((html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.head.innerHTML;
  }, []);

  // Reconstruct full HTML from body content
  const reconstructFullHtml = useCallback((bodyContent: string) => {
    const headContent = extractHeadContent(originalContentRef.current || htmlContent);
    return `<!DOCTYPE html>
<html lang="en">
<head>
${headContent}
</head>
<body>
${bodyContent}
</body>
</html>`;
  }, [htmlContent, extractHeadContent]);

  // Get responsive dimensions
  const getContainerDimensions = () => {
    switch (activeViewport) {
      case "tablet":
        return orientation === "portrait"
          ? { width: 768, height: 1024 }
          : { width: 1024, height: 768 };
      case "mobile":
        return orientation === "portrait"
          ? { width: 375, height: 667 }
          : { width: 667, height: 375 };
      default:
        return { width: "100%", height: "100%" }; // desktop
    }
  };

  // Handle content changes
  const handleContentChange = useCallback(() => {
    if (!contentRef.current || !onContentChange || !isEditing) return;

    const bodyContent = contentRef.current.innerHTML;
    const fullHtml = reconstructFullHtml(bodyContent);
    
    setHasChanges(true);
    onContentChange(fullHtml);
  }, [onContentChange, reconstructFullHtml, isEditing]);

  // Make text elements editable
  const makeTextEditable = useCallback(() => {
    if (!contentRef.current) return;

    const textSelectors = 'h1, h2, h3, h4, h5, h6, p, li, span:not([class*="icon"]):not([class*="sr-"]), div:not([class*="container"]):not([class*="wrapper"]):not([class*="flex"]):not([class*="grid"])';
    const textElements = contentRef.current.querySelectorAll(textSelectors);

    textElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      
      if (htmlElement.textContent?.trim() && !htmlElement.hasAttribute("contenteditable")) {
        htmlElement.setAttribute("contenteditable", "true");
        htmlElement.classList.add("editable-text");
        
        // Add event listeners
        htmlElement.addEventListener("input", handleContentChange);
        htmlElement.addEventListener("blur", handleContentChange);
        
        // Add visual feedback
        htmlElement.style.outline = "2px solid transparent";
        htmlElement.style.transition = "all 0.2s ease";
        
        const handleFocus = () => {
          htmlElement.style.outlineColor = "#3b82f6";
          htmlElement.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
        };
        
        const handleFocusOut = () => {
          htmlElement.style.outlineColor = "transparent";
          htmlElement.style.backgroundColor = "transparent";
        };
        
        htmlElement.addEventListener("focus", handleFocus);
        htmlElement.addEventListener("focusout", handleFocusOut);
        
        // Store handlers for cleanup
        (htmlElement as any)._focusHandler = handleFocus;
        (htmlElement as any)._focusOutHandler = handleFocusOut;
      }
    });
  }, [handleContentChange]);

  // Disable text editing
  const disableTextEditing = useCallback(() => {
    if (!contentRef.current) return;

    const editableElements = contentRef.current.querySelectorAll(".editable-text");
    editableElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      
      htmlElement.removeAttribute("contenteditable");
      htmlElement.classList.remove("editable-text");
      htmlElement.removeEventListener("input", handleContentChange);
      htmlElement.removeEventListener("blur", handleContentChange);
      
      // Remove focus handlers
      if ((htmlElement as any)._focusHandler) {
        htmlElement.removeEventListener("focus", (htmlElement as any)._focusHandler);
        htmlElement.removeEventListener("focusout", (htmlElement as any)._focusOutHandler);
        delete (htmlElement as any)._focusHandler;
        delete (htmlElement as any)._focusOutHandler;
      }
      
      // Reset styles
      htmlElement.style.outline = "";
      htmlElement.style.transition = "";
      htmlElement.style.outlineColor = "";
      htmlElement.style.backgroundColor = "";
    });
  }, [handleContentChange]);

  // Update content when htmlContent changes (but not during editing)
  useEffect(() => {
    if (!isEditing && contentRef.current) {
      const bodyContent = extractBodyContent(htmlContent);
      contentRef.current.innerHTML = bodyContent;
      originalContentRef.current = htmlContent;
    }
  }, [htmlContent, isEditing, extractBodyContent]);

  // Handle edit mode changes
  useEffect(() => {
    if (isEditMode) {
      setIsEditing(true);
      // Small delay to ensure content is rendered
      setTimeout(() => {
        makeTextEditable();
      }, 100);
    } else {
      disableTextEditing();
      setIsEditing(false);
      setHasChanges(false);
    }
  }, [isEditMode, makeTextEditable, disableTextEditing]);

  // Update parent component when hasChanges state changes
  useEffect(() => {
    if (onHasChanges) {
      onHasChanges(hasChanges);
    }
  }, [hasChanges, onHasChanges]);

  const { width, height } = getContainerDimensions();

  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-100">
      <div
        ref={containerRef}
        className="bg-white shadow-lg overflow-auto"
        style={{
          width: typeof width === "number" ? `${width}px` : width,
          height: typeof height === "number" ? `${height}px` : height,
          maxWidth: "100%",
          maxHeight: "100%",
          transform:
            typeof width === "number" && width > window.innerWidth * 0.9
              ? `scale(${(window.innerWidth * 0.9) / width})`
              : "none",
          transformOrigin: "top left",
        }}
      >
        <div
          ref={contentRef}
          className={`w-full h-full ${isEditMode ? "edit-mode" : ""}`}
          dangerouslySetInnerHTML={{ __html: extractBodyContent(htmlContent) }}
        />
        
        {/* Add styles for editing */}
        {isEditMode && (
          <style jsx>{`
            .edit-mode .editable-text:hover {
              background-color: rgba(59, 130, 246, 0.05) !important;
              outline: 1px dashed rgba(59, 130, 246, 0.3) !important;
            }
            .edit-mode .editable-text:focus {
              background-color: rgba(59, 130, 246, 0.1) !important;
              outline: 2px solid #3b82f6 !important;
            }
          `}</style>
        )}
      </div>
    </div>
  );
}
