"use client";

import { useRef, useEffect, useCallback, useState } from "react";

interface ImprovedEditablePreviewProps {
  htmlContent: string;
  isEditMode: boolean;
  activeViewport: "desktop" | "tablet" | "mobile";
  orientation: "portrait" | "landscape";
  onContentChange?: (html: string) => void;
  onHasChanges?: (hasChanges: boolean) => void;
}

export function ImprovedEditablePreview({
  htmlContent,
  isEditMode,
  activeViewport,
  orientation,
  onContentChange,
  onHasChanges,
}: ImprovedEditablePreviewProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const originalContentRef = useRef<string>("");
  const updateTimeoutRef = useRef<number>();
  const focusedElementRef = useRef<{
    selector: string;
    textContent: string;
  } | null>(null);

  // Get responsive dimensions
  const getContainerDimensions = () => {
    switch (activeViewport) {
      case "tablet":
        return orientation === "portrait"
          ? { width: 768, height: 1024 }
          : { width: 1024, height: 768 };
      case "mobile":
        return orientation === "portrait"
          ? { width: 375, height: 667 }
          : { width: 667, height: 375 };
      default:
        return { width: 1200, height: 800 }; // desktop
    }
  };

  // Extract body content from full HTML
  const extractBodyContent = useCallback((html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.body.innerHTML;
  }, []);

  // Extract head content from full HTML
  const extractHeadContent = useCallback((html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.head.innerHTML;
  }, []);

  // Reconstruct full HTML from body content
  const reconstructFullHtml = useCallback(
    (bodyContent: string) => {
      const headContent = extractHeadContent(
        originalContentRef.current || htmlContent
      );
      return `<!DOCTYPE html>
<html lang="en">
<head>
${headContent}
</head>
<body>
${bodyContent}
</body>
</html>`;
    },
    [htmlContent, extractHeadContent]
  );

  // Handle input changes (for hasChanges tracking)
  const handleInputChange = useCallback(() => {
    if (!isEditing) return;
    setHasChanges(true);
  }, [isEditing]);

  // Handle content changes with better debouncing
  const handleContentChange = useCallback(() => {
    const iframe = iframeRef.current;
    if (!iframe?.contentDocument?.body || !onContentChange || !isEditing)
      return;

    // Store focused element info before update
    const activeElement = iframe.contentDocument.activeElement as HTMLElement;
    if (activeElement && activeElement !== iframe.contentDocument.body) {
      const tagName = activeElement.tagName.toLowerCase();
      const textContent =
        activeElement.textContent?.trim().substring(0, 50) || "";
      focusedElementRef.current = { selector: tagName, textContent };
    }

    // Clear existing timeout
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    // Debounce updates but only on blur, not on input
    updateTimeoutRef.current = window.setTimeout(() => {
      if (iframe?.contentDocument?.body) {
        const bodyContent = iframe.contentDocument.body.innerHTML;
        const fullHtml = reconstructFullHtml(bodyContent);
        onContentChange(fullHtml);
      }
    }, 100); // Short delay just to batch rapid changes
  }, [onContentChange, reconstructFullHtml, isEditing]);

  // Make text elements editable
  const makeTextEditable = useCallback(() => {
    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return;

    const doc = iframe.contentDocument;
    const textSelectors =
      'h1, h2, h3, h4, h5, h6, p, li, span:not([class*="icon"]):not([class*="sr-"]), div:not([class*="container"]):not([class*="wrapper"]):not([class*="flex"]):not([class*="grid"])';
    const textElements = doc.querySelectorAll(textSelectors);

    textElements.forEach((element) => {
      const htmlElement = element as HTMLElement;

      if (
        htmlElement.textContent?.trim() &&
        !htmlElement.hasAttribute("contenteditable")
      ) {
        htmlElement.setAttribute("contenteditable", "true");
        htmlElement.classList.add("editable-text");

        // Listen to input for hasChanges tracking and blur for content updates
        htmlElement.addEventListener("input", handleInputChange);
        htmlElement.addEventListener("blur", handleContentChange);

        // Add visual feedback styles
        htmlElement.style.transition = "all 0.2s ease";
        htmlElement.addEventListener("mouseenter", () => {
          htmlElement.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
          htmlElement.style.outline = "1px dashed rgba(59, 130, 246, 0.3)";
        });
        htmlElement.addEventListener("mouseleave", () => {
          if (htmlElement !== doc.activeElement) {
            htmlElement.style.backgroundColor = "";
            htmlElement.style.outline = "";
          }
        });
        htmlElement.addEventListener("focus", () => {
          htmlElement.style.backgroundColor = "rgba(59, 130, 246, 0.1)";
          htmlElement.style.outline = "2px solid #3b82f6";
        });
        htmlElement.addEventListener("blur", () => {
          htmlElement.style.backgroundColor = "";
          htmlElement.style.outline = "";
        });
      }
    });
  }, [handleContentChange]);

  // Disable text editing
  const disableTextEditing = useCallback(() => {
    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return;

    const editableElements =
      iframe.contentDocument.querySelectorAll(".editable-text");
    editableElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      htmlElement.removeAttribute("contenteditable");
      htmlElement.classList.remove("editable-text");
      htmlElement.removeEventListener("input", handleInputChange);
      htmlElement.removeEventListener("blur", handleContentChange);

      // Reset styles
      htmlElement.style.backgroundColor = "";
      htmlElement.style.outline = "";
      htmlElement.style.transition = "";
    });
  }, [handleInputChange, handleContentChange]);

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return;

    const doc = iframe.contentDocument;

    // Write HTML content to iframe
    doc.open();
    doc.write(htmlContent);
    doc.close();

    if (isEditMode) {
      // Enable editing after content is loaded
      setTimeout(() => {
        makeTextEditable();

        // Restore focus if we have stored element info
        if (focusedElementRef.current) {
          const { selector, textContent } = focusedElementRef.current;
          const elements = doc.querySelectorAll(selector);
          for (const element of elements) {
            const htmlElement = element as HTMLElement;
            if (htmlElement.textContent?.trim().startsWith(textContent)) {
              htmlElement.focus();
              focusedElementRef.current = null;
              break;
            }
          }
        }
      }, 100);
    }
  }, [htmlContent, isEditMode, makeTextEditable]);

  // Update content when htmlContent changes (but not during editing)
  useEffect(() => {
    if (!isEditing) {
      originalContentRef.current = htmlContent;
      // Trigger iframe reload
      if (iframeRef.current) {
        handleIframeLoad();
      }
    }
  }, [htmlContent, isEditing, handleIframeLoad]);

  // Handle edit mode changes
  useEffect(() => {
    if (isEditMode) {
      setIsEditing(true);
      // Small delay to ensure iframe content is loaded
      setTimeout(() => {
        makeTextEditable();
      }, 150);
    } else {
      disableTextEditing();
      setIsEditing(false);
      setHasChanges(false);
    }
  }, [isEditMode, makeTextEditable, disableTextEditing]);

  // Update parent component when hasChanges state changes
  useEffect(() => {
    if (onHasChanges) {
      onHasChanges(hasChanges);
    }
  }, [hasChanges, onHasChanges]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const { width, height } = getContainerDimensions();

  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-100">
      <div
        className="bg-white shadow-lg"
        style={{
          width: `${width}px`,
          height: `${height}px`,
          maxWidth: "100%",
          maxHeight: "100%",
          transform:
            width > window.innerWidth * 0.9
              ? `scale(${(window.innerWidth * 0.9) / width})`
              : "none",
          transformOrigin: "top left",
        }}
      >
        <iframe
          ref={iframeRef}
          className="w-full h-full border-0"
          title="Page Preview"
          onLoad={handleIframeLoad}
          sandbox="allow-same-origin allow-scripts"
        />
      </div>
    </div>
  );
}
