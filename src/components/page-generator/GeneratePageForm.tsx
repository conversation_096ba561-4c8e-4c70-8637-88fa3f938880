"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { PageStyle, PageType } from "@/services/page-generator";
import { generatePage, GeneratePageFormData } from "@/actions/generate-page";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Form validation schema
const formSchema = z.object({
  productDescription: z
    .string()
    .min(10, "Product description must be at least 10 characters"),
  targetAudience: z
    .string()
    .min(5, "Target audience must be at least 5 characters"),
  keywords: z.string().min(3, "Keywords must be at least 3 characters"),
  type: z.nativeEnum(PageType),
  style: z.nativeEnum(PageStyle),
  language: z.string().min(2, "Language must be at least 2 characters"),
  additionalRequirements: z.string().optional(),
  leadCaptureCode: z.string().optional(),
});

/**
 * Landing page generation form component
 */
export function GeneratePageForm({
  onResult,
}: {
  onResult?: (result: {
    loading: boolean;
    html?: string;
    error?: string;
    isPartial?: boolean;
  }) => void;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define form
  const form = useForm<GeneratePageFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      productDescription: "",
      targetAudience: "",
      keywords: "",
      type: PageType.LEAD_GENERATION,
      style: PageStyle.MODERN,
      language: "English",
      additionalRequirements: "",
      leadCaptureCode: "",
    },
  });

  // Form submission handler
  const onSubmit = async (data: GeneratePageFormData) => {
    console.log("[DEBUG] Form submission started with data:", {
      ...data,
      leadCaptureCode: data.leadCaptureCode ? "[REDACTED]" : undefined,
    });

    setIsSubmitting(true);
    onResult?.({ loading: true });

    try {
      const result = await generatePage(data);
      console.log("[DEBUG] Generate page result:", {
        success: result.success,
        message: result.message,
        hasErrors: !!result.errors,
        hasData: !!result.data,
        dataId: result.data?.id,
        isPartial: result.data?.isPartial,
      });

      if (result.success && result.data) {
        if (result.data.isPartial) {
          toast.warning("Generated partial preview", {
            description:
              "The page generation is still in progress. Showing partial preview.",
          });
        } else {
          toast.success("Page generated successfully");
        }
        onResult?.({
          loading: false,
          html: result.data.html,
          isPartial: result.data.isPartial,
        });
      } else {
        console.error("[DEBUG] Generation failed:", {
          message: result.message,
          errors: result.errors,
        });

        toast.error("Failed to generate page", {
          description: result.message || "Please try again.",
        });
        onResult?.({
          loading: false,
          error: result.message || "Failed to generate page",
        });
      }
    } catch (error) {
      console.error("[DEBUG] Unexpected error in form submission:", {
        error,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
      });

      toast.error("An unexpected error occurred", {
        description: "Please try again.",
      });
      onResult?.({ loading: false, error: "Unexpected error" });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardDescription>
          Fill in the details below to generate a professional landing page for
          your product or service.{" "}
          <strong>The more details you provide, the better the result.</strong>
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Product Description */}
            <FormField
              control={form.control}
              name="productDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product/Service Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your product or service in detail"
                      {...field}
                      rows={4}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a clear and detailed description of what you&apos;re
                    offering.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Target Audience */}
            <FormField
              control={form.control}
              name="targetAudience"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target Audience</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your ideal customers"
                      {...field}
                      rows={3}
                    />
                  </FormControl>
                  <FormDescription>
                    Who is your product or service for? Be as specific as
                    possible.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Keywords */}
            <FormField
              control={form.control}
              name="keywords"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Keywords</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., marketing, conversion, affordable"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter keywords separated by commas
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Page Style */}
            <FormField
              control={form.control}
              name="style"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Design Style</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a design style" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={PageStyle.MODERN}>Modern</SelectItem>
                      <SelectItem value={PageStyle.MINIMAL}>Minimal</SelectItem>
                      <SelectItem value={PageStyle.BOLD}>Bold</SelectItem>
                      <SelectItem value={PageStyle.ELEGANT}>Elegant</SelectItem>
                      <SelectItem value={PageStyle.PLAYFUL}>Playful</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Language */}
            <FormField
              control={form.control}
              name="language"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Language</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., English" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter the language for your landing page
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Additional Requirements */}
            <FormField
              control={form.control}
              name="additionalRequirements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Requirements (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any specific requests or additional details"
                      {...field}
                      rows={3}
                    />
                  </FormControl>
                  <FormDescription>
                    Include any specific requirements or preferences for your
                    landing page
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Lead Capture Code - Hidden */}
            {/* <FormField
              control={form.control}
              name="leadCaptureCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Integration Code (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Paste your lead capture or other integration code here"
                      {...field}
                      rows={3}
                    />
                  </FormControl>
                  <FormDescription>
                    Paste code from your email marketing service or other
                    integration
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            /> */}
          </form>
        </Form>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => form.reset()}>
          Reset
        </Button>
        <Button
          type="submit"
          onClick={form.handleSubmit(onSubmit)}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Generating..." : "Generate Landing Page"}
        </Button>
      </CardFooter>
    </Card>
  );
}
