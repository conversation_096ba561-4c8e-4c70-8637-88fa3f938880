import * as React from "react";
import { toast } from "sonner";

export const useCaptcha = () => {
    const [token, setToken] = React.useState<string | null>(null);

    const handleVerify = React.useCallback((newToken: string) => {
        setToken(newToken);
    }, []);

    const handleError = React.useCallback(() => {
        setToken(null);
        toast.error("CAPTCHA Error", {
            description: "Failed to verify CAPTCHA. Please try again.",
        });
    }, []);

    const reset = React.useCallback(() => {
        setToken(null);
    }, []);

    return {
        token,
        handleVerify,
        handleError,
        reset,
        isVerified: !!token,
    };
};
