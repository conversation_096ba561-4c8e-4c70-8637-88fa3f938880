import { RefObject, useCallback, useEffect, useRef, useState } from "react";

interface UseSimpleTextEditorProps {
  containerRef: RefObject<HTMLElement | null>;
  onContentChange?: (html: string) => void;
}

export const useSimpleTextEditor = ({
  containerRef,
  onContentChange,
}: UseSimpleTextEditorProps) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const debounceTimeoutRef = useRef<number | undefined>(undefined);

  const handleTextInput = useCallback(() => {
    setHasChanges(true);

    // Debounced content change for input events (while user is typing)
    if (containerRef.current && onContentChange) {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        window.clearTimeout(debounceTimeoutRef.current);
      }

      // Debounce the content change callback to avoid frequent calls
      debounceTimeoutRef.current = window.setTimeout(() => {
        if (containerRef.current) {
          const updatedHtml = containerRef.current.innerHTML;
          onContentChange(updatedHtml);
        }
      }, 300); // 300ms debounce
    }
  }, [containerRef, onContentChange]);

  // Immediate content change for blur events (when user finishes editing)
  const handleTextBlur = useCallback(() => {
    if (containerRef.current && onContentChange) {
      // Clear debounce timeout
      if (debounceTimeoutRef.current) {
        window.clearTimeout(debounceTimeoutRef.current);
      }

      // Immediately update content on blur
      const updatedHtml = containerRef.current.innerHTML;
      onContentChange(updatedHtml);
    }
  }, [containerRef, onContentChange]);

  const makeTextEditable = useCallback(() => {
    if (!containerRef.current) return;

    // Znajdź wszystkie elementy tekstowe
    const textSelectors =
      'h1, h2, h3, h4, h5, h6, p, li, span:not([class*="icon"]):not([class*="sr-"]), div:not([class*="container"]):not([class*="wrapper"]):not([class*="flex"]):not([class*="grid"])';
    const textElements = containerRef.current.querySelectorAll(textSelectors);

    textElements.forEach((element) => {
      const htmlElement = element as HTMLElement;

      // Sprawdź czy element ma text content (nie jest pusty ani nie zawiera tylko elementów)
      if (htmlElement.textContent?.trim()) {
        htmlElement.setAttribute("contenteditable", "true");
        htmlElement.classList.add("editable-text");

        // Dodaj event listener dla zmian
        htmlElement.addEventListener("blur", handleTextBlur);
        htmlElement.addEventListener("input", handleTextInput);
      }
    });
  }, [containerRef, handleTextBlur, handleTextInput]);

  const disableTextEditing = useCallback(() => {
    if (!containerRef.current) return;

    const editableElements = containerRef.current.querySelectorAll(
      ".editable-text",
    );
    editableElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      htmlElement.removeAttribute("contenteditable");
      htmlElement.classList.remove("editable-text");
      htmlElement.removeEventListener("blur", handleTextBlur);
      htmlElement.removeEventListener("input", handleTextInput);
    });
  }, [containerRef, handleTextBlur, handleTextInput]);

  const enableEditMode = useCallback(() => {
    setIsEditMode(true);
    setTimeout(makeTextEditable, 100); // Małe opóźnienie dla pewności
  }, [makeTextEditable]);

  const disableEditMode = useCallback(() => {
    setIsEditMode(false);
    disableTextEditing();
    setHasChanges(false);
  }, [disableTextEditing]);

  // Cleanup przy unmount
  useEffect(() => {
    return () => {
      // Clear timeout on unmount
      if (debounceTimeoutRef.current) {
        window.clearTimeout(debounceTimeoutRef.current);
      }
      disableTextEditing();
    };
  }, [disableTextEditing]);

  return {
    isEditMode,
    hasChanges,
    enableEditMode,
    disableEditMode,
    makeTextEditable,
    disableTextEditing,
  };
};
