import { RefObject, useCallback, useEffect, useRef, useState } from "react";

interface UseSimpleTextEditorProps {
  containerRef: RefObject<HTMLElement | null>;
  onContentChange?: (html: string) => void;
}

export const useSimpleTextEditor = ({
  containerRef,
  onContentChange,
}: UseSimpleTextEditorProps) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const debounceTimeoutRef = useRef<number | undefined>(undefined);
  const activeElementRef = useRef<HTMLElement | null>(null);
  const isUpdatingContentRef = useRef<boolean>(false);

  const handleTextInput = useCallback((event: Event) => {
    // Store reference to currently focused element
    const target = event.target as HTMLElement;
    activeElementRef.current = target;

    setHasChanges(true);

    // Debounced content change for input events (while user is typing)
    if (
      containerRef.current && onContentChange && !isUpdatingContentRef.current
    ) {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        window.clearTimeout(debounceTimeoutRef.current);
      }

      // Longer debounce to reduce frequent updates that can cause focus loss
      debounceTimeoutRef.current = window.setTimeout(() => {
        if (containerRef.current && !isUpdatingContentRef.current) {
          isUpdatingContentRef.current = true;
          const updatedHtml = containerRef.current.innerHTML;
          onContentChange(updatedHtml);

          // Reset flag after a short delay
          setTimeout(() => {
            isUpdatingContentRef.current = false;
          }, 100);
        }
      }, 800); // Increased debounce to 800ms
    }
  }, [containerRef, onContentChange]);

  // Immediate content change for blur events (when user finishes editing)
  const handleTextBlur = useCallback((event: Event) => {
    const target = event.target as HTMLElement;

    if (
      containerRef.current && onContentChange && !isUpdatingContentRef.current
    ) {
      // Clear debounce timeout
      if (debounceTimeoutRef.current) {
        window.clearTimeout(debounceTimeoutRef.current);
      }

      // Immediately update content on blur
      isUpdatingContentRef.current = true;
      const updatedHtml = containerRef.current.innerHTML;
      onContentChange(updatedHtml);

      // Reset flag after a short delay
      setTimeout(() => {
        isUpdatingContentRef.current = false;
      }, 100);
    }

    // Clear active element reference when blurring
    if (activeElementRef.current === target) {
      activeElementRef.current = null;
    }
  }, [containerRef, onContentChange]);

  const makeTextEditable = useCallback(() => {
    if (!containerRef.current) return;

    // Znajdź wszystkie elementy tekstowe
    const textSelectors =
      'h1, h2, h3, h4, h5, h6, p, li, span:not([class*="icon"]):not([class*="sr-"]), div:not([class*="container"]):not([class*="wrapper"]):not([class*="flex"]):not([class*="grid"])';
    const textElements = containerRef.current.querySelectorAll(textSelectors);

    textElements.forEach((element) => {
      const htmlElement = element as HTMLElement;

      // Sprawdź czy element ma text content (nie jest pusty ani nie zawiera tylko elementów)
      if (
        htmlElement.textContent?.trim() &&
        !htmlElement.hasAttribute("contenteditable")
      ) {
        htmlElement.setAttribute("contenteditable", "true");
        htmlElement.classList.add("editable-text");

        // Dodaj event listener dla zmian z proper typing
        htmlElement.addEventListener("blur", handleTextBlur as EventListener);
        htmlElement.addEventListener("input", handleTextInput as EventListener);

        // Add focus styles for better UX
        htmlElement.style.outline = "2px solid transparent";
        htmlElement.style.transition = "outline-color 0.2s ease";

        // Add focus and blur handlers for visual feedback
        const handleFocus = () => {
          htmlElement.style.outlineColor = "#3b82f6";
          htmlElement.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
        };

        const handleFocusOut = () => {
          htmlElement.style.outlineColor = "transparent";
          htmlElement.style.backgroundColor = "transparent";
        };

        htmlElement.addEventListener("focus", handleFocus);
        htmlElement.addEventListener("focusout", handleFocusOut);

        // Store focus handlers for cleanup
        (htmlElement as HTMLElement & {
          _focusHandler?: () => void;
          _focusOutHandler?: () => void;
        })._focusHandler = handleFocus;
        (htmlElement as HTMLElement & {
          _focusHandler?: () => void;
          _focusOutHandler?: () => void;
        })._focusOutHandler = handleFocusOut;
      }
    });
  }, [containerRef, handleTextBlur, handleTextInput]);

  const disableTextEditing = useCallback(() => {
    if (!containerRef.current) return;

    const editableElements = containerRef.current.querySelectorAll(
      ".editable-text",
    );
    editableElements.forEach((element) => {
      const htmlElement = element as HTMLElement & {
        _focusHandler?: () => void;
        _focusOutHandler?: () => void;
      };

      htmlElement.removeAttribute("contenteditable");
      htmlElement.classList.remove("editable-text");
      htmlElement.removeEventListener("blur", handleTextBlur as EventListener);
      htmlElement.removeEventListener(
        "input",
        handleTextInput as EventListener,
      );

      // Remove focus handlers if they exist
      if (htmlElement._focusHandler) {
        htmlElement.removeEventListener("focus", htmlElement._focusHandler);
        delete htmlElement._focusHandler;
      }
      if (htmlElement._focusOutHandler) {
        htmlElement.removeEventListener(
          "focusout",
          htmlElement._focusOutHandler,
        );
        delete htmlElement._focusOutHandler;
      }

      // Reset styles
      htmlElement.style.outline = "";
      htmlElement.style.transition = "";
      htmlElement.style.outlineColor = "";
      htmlElement.style.backgroundColor = "";
    });
  }, [containerRef, handleTextBlur, handleTextInput]);

  const enableEditMode = useCallback(() => {
    setIsEditMode(true);
    setTimeout(makeTextEditable, 100); // Małe opóźnienie dla pewności
  }, [makeTextEditable]);

  const disableEditMode = useCallback(() => {
    setIsEditMode(false);
    disableTextEditing();
    setHasChanges(false);
  }, [disableTextEditing]);

  // Cleanup przy unmount
  useEffect(() => {
    return () => {
      // Clear timeout on unmount
      if (debounceTimeoutRef.current) {
        window.clearTimeout(debounceTimeoutRef.current);
      }
      disableTextEditing();
    };
  }, [disableTextEditing]);

  return {
    isEditMode,
    hasChanges,
    enableEditMode,
    disableEditMode,
    makeTextEditable,
    disableTextEditing,
  };
};
