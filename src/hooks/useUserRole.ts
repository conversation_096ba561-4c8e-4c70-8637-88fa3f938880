"use client";

import { useEffect, useState } from "react";
import { createClient } from "@/lib/supabase/client";
import { UserRole } from "@/types";

// Type guard to validate UserRole
const isValidUserRole = (role: unknown): role is UserRole => {
    return typeof role === "string" &&
        (role === "admin" || role === "subscriber");
};

export function useUserRole() {
    const [role, setRole] = useState<UserRole | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const supabase = createClient();

        const getRole = async () => {
            try {
                const { data: { user }, error } = await supabase.auth.getUser();

                if (error || !user) {
                    setRole(null);
                    return;
                }

                // Safely validate and extract user role
                const rawRole = user.user_metadata?.role;
                const userRole: UserRole = isValidUserRole(rawRole)
                    ? rawRole
                    : "subscriber";
                setRole(userRole);
            } catch (error) {
                console.error("Error fetching user role:", error);
                setRole(null);
            } finally {
                setIsLoading(false);
            }
        };

        getRole();

        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
            async (event, session) => {
                if (event === "SIGNED_IN" && session?.user) {
                    // Safely validate and extract user role
                    const rawRole = session.user.user_metadata?.role;
                    const userRole: UserRole = isValidUserRole(rawRole)
                        ? rawRole
                        : "subscriber";
                    setRole(userRole);
                } else if (event === "SIGNED_OUT") {
                    setRole(null);
                }
                setIsLoading(false);
            },
        );

        return () => subscription.unsubscribe();
    }, []);

    return {
        role,
        isLoading,
        isAdmin: role === "admin",
        isSubscriber: role === "subscriber",
    };
}
