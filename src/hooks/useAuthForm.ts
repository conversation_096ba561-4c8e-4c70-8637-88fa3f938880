import * as React from "react";
import {
    type DefaultValues,
    type SubmitHandler,
    useForm,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { z } from "zod";
import { useFormErrors } from "./useFormErrors";
import { type ActionResult, useFormSubmit } from "./useFormSubmit";

interface UseAuthFormOptions<T extends Record<string, unknown>> {
    schema: z.ZodSchema<T>;
    defaultValues: DefaultValues<T>;
    onSuccess?: () => void;
    onError?: (error: Error) => void;
    action: (data: T) => Promise<ActionResult<T>>;
}

export const useAuthForm = <T extends Record<string, unknown>>({
    schema,
    defaultValues,
    onSuccess,
    onError,
    action,
}: UseAuthFormOptions<T>) => {
    const form = useForm<T>({
        resolver: zodResolver(schema),
        defaultValues,
    });

    const { setServerErrors, clearServerErrors } = useFormErrors(form);
    const { submit, isPending } = useFormSubmit(action, {
        onSuccess: () => {
            clearServerErrors();
            onSuccess?.();
        },
        onError,
        onFieldErrors: setServerErrors,
    });

    const handleSubmit: SubmitHandler<T> = React.useCallback(
        async (data: T) => {
            clearServerErrors();
            const result = await submit(data);
            if (result.fieldErrors) {
                setServerErrors(result.fieldErrors);
            }
        },
        [submit, setServerErrors, clearServerErrors],
    );

    return {
        form,
        isPending,
        handleSubmit,
    };
};
