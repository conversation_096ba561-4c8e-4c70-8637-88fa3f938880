import * as React from "react";
import type { Path, UseFormReturn } from "react-hook-form";
import type { ActionResult } from "./useFormSubmit";

export const useFormErrors = <T extends Record<string, unknown>>(
    form: UseFormReturn<T>,
) => {
    const setServerErrors = React.useCallback(
        (fieldErrors?: ActionResult<T>["fieldErrors"]) => {
            if (!fieldErrors) return;

            Object.entries(fieldErrors).forEach(([field, errors]) => {
                if (errors?.length) {
                    form.setError(field as Path<T>, {
                        type: "server",
                        message: errors.join(", "),
                    });
                }
            });
        },
        [form],
    );

    const clearServerErrors = React.useCallback(() => {
        form.clearErrors();
    }, [form]);

    return { setServerErrors, clearServerErrors };
};
