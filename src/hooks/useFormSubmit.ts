import * as React from "react";
import { toast } from "sonner";

export interface ActionResult<
    T extends Record<string, unknown> = Record<string, unknown>,
> {
    success: boolean;
    message?: string;
    fieldErrors?: Partial<Record<keyof T, string[]>>;
}

interface FormSubmitOptions<T extends Record<string, unknown>> {
    onSuccess?: () => void;
    onError?: (error: Error) => void;
    successMessage?: string;
    errorMessage?: string;
    onFieldErrors?: (
        fieldErrors: NonNullable<ActionResult<T>["fieldErrors"]>,
    ) => void;
}

export const useFormSubmit = <T extends Record<string, unknown>>(
    action: (data: T) => Promise<ActionResult<T>>,
    options?: FormSubmitOptions<T>,
) => {
    const [isPending, startTransition] = React.useTransition();

    const submit = React.useCallback(async (data: T) => {
        let result: ActionResult<T>;

        startTransition(async () => {
            try {
                result = await action(data);

                if (result.success) {
                    toast.success("Success", {
                        description: options?.successMessage ?? result.message,
                    });
                    options?.onSuccess?.();
                } else {
                    if (result.fieldErrors && options?.onFieldErrors) {
                        options.onFieldErrors(result.fieldErrors);
                    }
                    toast.error("Error", {
                        description: options?.errorMessage ?? result.message,
                    });
                    options?.onError?.(new Error(result.message));
                }
            } catch (error) {
                const message = error instanceof Error
                    ? error.message
                    : "An unknown error occurred";
                toast.error("Error", { description: message });
                options?.onError?.(error as Error);
                result = { success: false, message } as ActionResult<T>;
            }
        });

        return result!;
    }, [action, options]);

    return { submit, isPending };
};
