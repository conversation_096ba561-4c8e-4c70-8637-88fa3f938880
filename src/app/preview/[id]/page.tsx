import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { supabase } from "@/db/supabase";
import { format } from "date-fns";
import Link from "next/link";
import { PageConfig } from "@/types";
import DeleteButton from "@/components/page-details/DeleteButton";
import { PagePreviewSection } from "@/components/page-details/PagePreviewSection";
import { AdminOnly } from "@/components/auth/RoleGuard";

export const runtime = "edge";
export const dynamicParams = false;

export default async function PageDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Ensure user is authenticated
  const client = await createClient();
  const { data: userData, error: authError } = await client.auth.getUser();
  if (authError || !userData?.user) {
    redirect("/login");
  }
  const { id } = await params;
  const pageId = parseInt(id);
  if (isNaN(pageId)) {
    redirect("/dashboard");
  }

  // Fetch page details
  const { data: page, error: pageError } = await supabase
    .from("pages")
    .select("*")
    .eq("id", pageId)
    .maybeSingle();

  if (pageError || !page) {
    redirect("/dashboard");
  }

  // Fetch log data to get model information and generation details
  const { data: logData } = await supabase
    .from("logs")
    .select(
      "model, total_cost, cost_currency, prompt_tokens, completion_tokens, total_tokens, generation_duration_ms, generation_attempts, status, created_at"
    )
    .eq("page_id", pageId)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  // Parse config
  const config = page.config as unknown as PageConfig;

  return (
    <main className="container mx-auto py-12">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <Link
              href="/history"
              className="text-primary hover:underline mb-4 inline-block"
            >
              ← Back to History
            </Link>
            <h1 className="text-3xl font-bold mb-2">Generated Page Details</h1>
            <p className="text-muted-foreground">
              Created on {format(new Date(page.created_at), "PPP")}
            </p>
          </div>
          <div className="flex gap-2">
            <a
              href={`/api/preview/${page.id}/download`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary cursor-pointer"
              download
            >
              Download HTML
            </a>
            <DeleteButton
              id={page.id}
              title={config.keywords.join(", ")}
              type={config.type}
            />
          </div>
        </div>

        <div className="bg-white shadow rounded-lg overflow-hidden mb-8">
          <div className="border-b px-6 py-4">
            <h2 className="text-xl font-semibold">Page Configuration</h2>
          </div>
          <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Type</h3>
              <p className="mt-1 text-base">{config?.type || "N/A"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">
                Target Audience
              </h3>
              <p className="mt-1 text-base">
                {config?.targetAudience || "N/A"}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Keywords</h3>
              <p className="mt-1 text-base">
                {config?.keywords && Array.isArray(config.keywords)
                  ? config.keywords.join(", ")
                  : "N/A"}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">
                Design Style
              </h3>
              <p className="mt-1 text-base">{config?.style || "N/A"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Language</h3>
              <p className="mt-1 text-base">{config?.language || "N/A"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">
                Lead Capture Integration
              </h3>
              <p className="mt-1 text-base">
                {config?.integrations?.leadCapture
                  ? "Configured"
                  : "Not configured"}
              </p>
            </div>
            {logData && (
              <>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    AI Model
                  </h3>
                  <p className="mt-1 text-base font-mono text-sm">
                    {logData.model || "Unknown"}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Generation Time
                  </h3>
                  <p className="mt-1 text-base">
                    {logData.generation_duration_ms
                      ? `${(logData.generation_duration_ms / 1000).toFixed(2)}s`
                      : "N/A"}
                  </p>
                </div>
                {logData.total_tokens && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Token Usage
                    </h3>
                    <p className="mt-1 text-base">
                      {logData.total_tokens.toLocaleString()} tokens
                      {logData.prompt_tokens && logData.completion_tokens && (
                        <span className="text-sm text-gray-500 block">
                          {logData.prompt_tokens.toLocaleString()} prompt +{" "}
                          {logData.completion_tokens.toLocaleString()}{" "}
                          completion
                        </span>
                      )}
                    </p>
                  </div>
                )}
                {logData.total_cost && (
                  <AdminOnly>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Generation Cost
                      </h3>
                      <p className="mt-1 text-base">
                        ${logData.total_cost.toFixed(6)}{" "}
                        {logData.cost_currency || "USD"}
                        {logData.generation_attempts &&
                          logData.generation_attempts > 1 && (
                            <span className="text-sm text-gray-500 block">
                              {logData.generation_attempts} attempts
                            </span>
                          )}
                      </p>
                    </div>
                  </AdminOnly>
                )}
              </>
            )}
          </div>
        </div>

        <PagePreviewSection 
          htmlContent={page.html_content} 
          pageId={pageId}
          pageConfig={config}
        />
      </div>
    </main>
  );
}
