import { NextResponse } from "next/server";
import { TokenBucket } from "@/services/openrouter/rate-limiter";
import { UserLimitsService } from "@/lib/user-limits";
import { createClient } from "@/lib/supabase/server";

// Singleton instance of TokenBucket for rate limiting (technical rate limit)
const rateLimiter = new TokenBucket(10, 1, 1000, 100); // Keep high for technical rate limiting

export async function GET() {
    try {
        // Get authenticated user
        const supabase = await createClient();
        const { data: userData, error: authError } = await supabase.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                {
                    error:
                        "Unauthorized - You must be logged in to view rate limits.",
                },
                { status: 401 },
            );
        }

        const userId = userData.user.id;
        const userLimitsService = UserLimitsService.getInstance();

        // Get user-specific limits
        const userLimits = await userLimitsService.getUserLimits(userId);

        // Get technical rate limit stats
        const technicalStats = await rateLimiter.getUsageStats();

        return NextResponse.json({
            // User daily limits (what users care about)
            dailyUsage: userLimits.dailyUsage,
            dailyLimit: userLimits.dailyPageLimit,
            remainingToday: userLimits.remainingToday,
            nextResetTime: userLimits.nextResetTime,
            nextResetIn: new Date(userLimits.nextResetTime).toLocaleTimeString(
                [],
                {
                    hour: "2-digit",
                    minute: "2-digit",
                },
            ),
            // Technical rate limiting (for burst protection)
            currentTokens: technicalStats.currentTokens,
            maxTokens: technicalStats.maxTokens,
        });
    } catch (error) {
        console.error("Error fetching rate limit info:", error);
        return NextResponse.json(
            { error: "Failed to fetch rate limit information" },
            { status: 500 },
        );
    }
}
