import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { imageValidator } from "@/services/image-validator";
import { z } from "zod";

export const runtime = "edge";

const validateImagesSchema = z.object({
    pageId: z.number().int().positive().optional(),
    html: z.string().min(1).optional(),
    replaceInvalid: z.boolean().default(false),
}).refine(
    (data) => data.pageId !== undefined || data.html !== undefined,
    {
        message: "Either pageId or html must be provided",
    },
);

export async function POST(request: NextRequest) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        // Parse and validate request body
        const body = await request.json();
        const validation = validateImagesSchema.safeParse(body);

        if (!validation.success) {
            return NextResponse.json(
                {
                    error: "Invalid request data",
                    details: validation.error.format(),
                },
                { status: 400 },
            );
        }

        const { pageId, html: providedHtml, replaceInvalid } = validation.data;
        let htmlContent = providedHtml;

        // If pageId is provided, fetch HTML from database
        if (pageId) {
            const { data: page, error: pageError } = await client
                .from("pages")
                .select("html_content")
                .eq("id", pageId)
                .eq("user_id", userData.user.id)
                .maybeSingle();

            if (pageError) {
                console.error("Database error:", pageError);
                return NextResponse.json(
                    { error: "Failed to fetch page" },
                    { status: 500 },
                );
            }

            if (!page) {
                return NextResponse.json(
                    { error: "Page not found or access denied" },
                    { status: 404 },
                );
            }

            htmlContent = page.html_content;
        }

        if (!htmlContent) {
            return NextResponse.json(
                { error: "No HTML content to validate" },
                { status: 400 },
            );
        }

        // Perform validation
        if (replaceInvalid) {
            const result = await imageValidator.validateAndFixHtml(htmlContent);

            // If pageId was provided and images were replaced, update the database
            if (pageId && result.summary.replacedImages > 0) {
                const { error: updateError } = await client
                    .from("pages")
                    .update({ html_content: result.html })
                    .eq("id", pageId)
                    .eq("user_id", userData.user.id);

                if (updateError) {
                    console.error(
                        "Failed to update page with fixed images:",
                        updateError,
                    );
                    // Don't fail the request, just log the error
                }
            }

            return NextResponse.json({
                success: true,
                html: result.html,
                summary: result.summary,
                replaced: result.summary.replacedImages > 0,
            });
        } else {
            // Just validate without replacing
            const summary = await imageValidator.getValidationStats(
                htmlContent,
            );

            return NextResponse.json({
                success: true,
                summary,
                needsFixing: summary.invalidImages > 0,
            });
        }
    } catch (error) {
        console.error("Image validation error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
