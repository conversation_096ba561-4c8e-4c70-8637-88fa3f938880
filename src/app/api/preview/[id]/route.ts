import { NextResponse } from "next/server";
import { PageConfig, PageResponse } from "@/types";
import { z } from "zod";
import type { Database } from "@/db/database.types";
import { createClient } from "@/lib/supabase/server";

export const runtime = "edge";

// Update schema for page config
const updatePageConfigSchema = z.object({
    type: z.string().min(1).optional(),
    targetAudience: z.string().min(1).optional(),
    keywords: z.array(z.string()).optional(),
    style: z.string().min(1).optional(),
    language: z.string().min(2).optional(),
    integrations: z.object({
        leadCapture: z.string(),
    }).optional(),
});

// Update schema for the entire request body
const updatePageSchema = z.object({
    config: updatePageConfigSchema.optional(),
    html_content: z.string().optional(),
});

export async function GET(
    _request: Request,
    { params: paramsPromise }: { params: Promise<{ id: string }> },
) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();
        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const { id } = await paramsPromise;
        const pageId = parseInt(id);

        if (isNaN(pageId)) {
            return NextResponse.json(
                { error: "Invalid page ID" },
                { status: 400 },
            );
        }

        const { data: page, error } = await client
            .from("pages")
            .select("*")
            .eq("id", pageId)
            .eq("user_id", userData.user.id)
            .maybeSingle();

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to fetch page" },
                { status: 500 },
            );
        }

        if (!page) {
            return NextResponse.json(
                {
                    error:
                        "Page not found or you don't have permission to access it",
                },
                { status: 404 },
            );
        }

        const response: PageResponse = {
            id: page.id,
            config: JSON.parse(JSON.stringify(page.config)) as PageConfig,
            html_content: page.html_content,
            created_at: page.created_at,
            parent_page_id: page.parent_page_id,
            status: "PENDING",
        };

        return NextResponse.json(response, {
            status: 200,
            headers: {
                "Cache-Control": "public, max-age=60",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function PATCH(
    request: Request,
    { params: paramsPromise }: { params: Promise<{ id: string }> },
) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();
        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const { id } = await paramsPromise;
        const pageId = parseInt(id);

        if (isNaN(pageId)) {
            return NextResponse.json(
                { error: "Invalid page ID" },
                { status: 400 },
            );
        }

        const body = await request.json();
        const result = updatePageSchema.safeParse(body);

        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid update data",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { data: page, error: fetchError } = await client
            .from("pages")
            .select("*")
            .eq("id", pageId)
            .eq("user_id", userData.user.id)
            .maybeSingle();

        if (fetchError) {
            console.error("Database error:", fetchError);
            return NextResponse.json(
                { error: "Failed to fetch page" },
                { status: 500 },
            );
        }

        if (!page) {
            return NextResponse.json(
                {
                    error:
                        "Page not found or you don't have permission to update it",
                },
                { status: 404 },
            );
        }

        const updateData: Partial<
            Database["public"]["Tables"]["pages"]["Update"]
        > = {};

        if (result.data.config) {
            const currentConfig = JSON.parse(
                JSON.stringify(page.config),
            ) as PageConfig;
            updateData.config = {
                ...currentConfig,
                ...result.data.config,
            } as unknown as Database["public"]["Tables"]["pages"]["Update"][
                "config"
            ];
        }

        if (result.data.html_content !== undefined) {
            updateData.html_content = result.data.html_content;
        }

        const { data: updatedPage, error: updateError } = await client
            .from("pages")
            .update(updateData)
            .eq("id", pageId)
            .eq("user_id", userData.user.id)
            .select()
            .maybeSingle();

        if (updateError) {
            console.error("Database error:", updateError);
            return NextResponse.json(
                { error: "Failed to update page" },
                { status: 500 },
            );
        }

        if (!updatedPage) {
            return NextResponse.json(
                { error: "Page not found after update" },
                { status: 404 },
            );
        }

        const response: PageResponse = {
            id: updatedPage.id,
            config: JSON.parse(
                JSON.stringify(updatedPage.config),
            ) as PageConfig,
            html_content: updatedPage.html_content,
            created_at: updatedPage.created_at,
            parent_page_id: updatedPage.parent_page_id,
            status: "PENDING",
        };

        return NextResponse.json(response, {
            status: 200,
            headers: {
                "Cache-Control": "no-cache",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function DELETE(
    _request: Request,
    { params: paramsPromise }: { params: Promise<{ id: string }> },
) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        console.log("[DELETE /api/preview/[id]] User data:", userData);
        console.log("[DELETE /api/preview/[id]] Auth error:", authError);

        if (authError || !userData?.user) {
            console.error("[DELETE /api/preview/[id]] Authentication failed.");
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const { id } = await paramsPromise;
        const pageId = parseInt(id);

        console.log("[DELETE /api/preview/[id]] Parsed page ID:", pageId);

        if (isNaN(pageId)) {
            console.error("[DELETE /api/preview/[id]] Invalid page ID:", id);
            return NextResponse.json(
                { error: "Invalid page ID" },
                { status: 400 },
            );
        }

        // Check if page exists and belongs to the current user
        console.log(
            "[DELETE /api/preview/[id]] Fetching page with ID:",
            pageId,
            "for user ID:",
            userData.user.id,
        );
        const { data: page, error: fetchError } = await client
            .from("pages")
            .select("*")
            .eq("id", pageId)
            .eq("user_id", userData.user.id)
            .maybeSingle();

        console.log("[DELETE /api/preview/[id]] Fetched page:", page);
        console.log("[DELETE /api/preview/[id]] Fetch error:", fetchError);

        if (fetchError) {
            console.error(
                "[DELETE /api/preview/[id]] Database error while fetching page:",
                fetchError,
            );
            return NextResponse.json(
                { error: "Failed to fetch page" },
                { status: 500 },
            );
        }

        if (!page) {
            console.warn(
                "[DELETE /api/preview/[id]] Page not found or permission denied. Page ID:",
                pageId,
                "User ID:",
                userData.user.id,
            );
            return NextResponse.json(
                {
                    error:
                        "Page not found or you don't have permission to delete it",
                },
                { status: 404 },
            );
        }

        // Delete page
        console.log(
            "[DELETE /api/preview/[id]] Attempting to delete page with ID:",
            pageId,
            "for user ID:",
            userData.user.id,
        );
        const { error: deleteError } = await client
            .from("pages")
            .delete()
            .eq("id", pageId)
            .eq("user_id", userData.user.id);

        console.log("[DELETE /api/preview/[id]] Delete error:", deleteError);

        if (deleteError) {
            console.error(
                "[DELETE /api/preview/[id]] Database error while deleting page:",
                deleteError,
            );
            return NextResponse.json(
                { error: "Failed to delete page" },
                { status: 500 },
            );
        }

        console.log(
            "[DELETE /api/preview/[id]] Page deleted successfully. Page ID:",
            pageId,
        );
        return new NextResponse(null, { status: 204 });
    } catch (error) {
        console.error("[DELETE /api/preview/[id]] Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
