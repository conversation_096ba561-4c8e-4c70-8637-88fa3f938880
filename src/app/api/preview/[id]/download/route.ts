import { NextResponse } from "next/server";
import { PageConfig } from "@/types";
import { createClient } from "@/lib/supabase/server";
import { formatHtml } from "@/services/page-generator/html-processor";

export const runtime = "edge";

export async function GET(
    _request: Request,
    { params: paramsPromise }: { params: Promise<{ id: string }> },
) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();
        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const { id } = await paramsPromise;
        const pageId = parseInt(id);

        if (isNaN(pageId)) {
            return NextResponse.json(
                { error: "Invalid page ID" },
                { status: 400 },
            );
        }

        const { data: page, error } = await client
            .from("pages")
            .select("*")
            .eq("id", pageId)
            .eq("user_id", userData.user.id)
            .maybeSingle();

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to fetch page" },
                { status: 500 },
            );
        }

        if (!page) {
            return NextResponse.json(
                {
                    error:
                        "Page not found or you don't have permission to access it",
                },
                { status: 404 },
            );
        }

        if (!page.html_content) {
            return NextResponse.json(
                { error: "No HTML content available" },
                { status: 404 },
            );
        }

        // Ensure HTML has the static head template
        const processedHtml = formatHtml(page.html_content);

        // Create a filename based on the page keywords or ID
        let filename = "landing-page";
        try {
            const config = page.config as unknown as PageConfig;
            if (
                config && config.keywords && Array.isArray(config.keywords) &&
                config.keywords.length > 0
            ) {
                filename = config.keywords[0].toLowerCase().replace(
                    /[^a-z0-9]/g,
                    "-",
                );
            }
        } catch (e) {
            console.error("Error creating filename from keywords:", e);
        }

        // Append ID to ensure uniqueness
        filename = `${filename}-${page.id}.html`;

        return new NextResponse(processedHtml, {
            status: 200,
            headers: {
                "Content-Type": "text/html; charset=utf-8",
                "Content-Disposition": `attachment; filename="${filename}"`,
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
