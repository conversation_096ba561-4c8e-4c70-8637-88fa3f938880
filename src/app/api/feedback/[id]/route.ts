import { NextResponse } from "next/server";
import { supabase } from "@/db/supabase";
import { createClient } from "@/lib/supabase/server";
import { z } from "zod";

export const runtime = "edge";

// Schema for updating feedback
const updateFeedbackSchema = z.object({
    rating: z.boolean().optional(),
    comment: z.string().optional(),
});

export async function GET(
    _request: Request,
    { params: paramsPromise }: { params: Promise<{ id: string }> },
) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const params = await paramsPromise;
        const { data: feedback, error } = await supabase
            .from("feedback")
            .select("*")
            .eq("id", params.id)
            .single();

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to fetch feedback" },
                { status: 500 },
            );
        }

        if (!feedback) {
            return NextResponse.json(
                { error: "Feedback not found" },
                { status: 404 },
            );
        }

        return NextResponse.json(feedback, {
            status: 200,
            headers: {
                "Cache-Control": "public, max-age=60",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function PATCH(
    request: Request,
    { params: paramsPromise }: { params: Promise<{ id: string }> },
) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const params = await paramsPromise;
        const body = await request.json();
        const result = updateFeedbackSchema.safeParse(body);

        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid feedback data",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { data: feedback, error } = await supabase
            .from("feedback")
            .update(result.data)
            .eq("id", params.id)
            .select()
            .single();

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to update feedback" },
                { status: 500 },
            );
        }

        if (!feedback) {
            return NextResponse.json(
                { error: "Feedback not found" },
                { status: 404 },
            );
        }

        return NextResponse.json(feedback);
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function DELETE(
    _request: Request,
    { params: paramsPromise }: { params: Promise<{ id: string }> },
) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const params = await paramsPromise;
        const { error } = await supabase
            .from("feedback")
            .delete()
            .eq("id", params.id);

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to delete feedback" },
                { status: 500 },
            );
        }

        return new NextResponse(null, { status: 204 });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
