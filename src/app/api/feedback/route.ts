import { NextResponse } from "next/server";
import { supabase, supabaseAdmin } from "@/db/supabase";
import { createClient } from "@/lib/supabase/server";
import { z } from "zod";

export const runtime = "edge";

// Schema for creating/updating feedback
const feedbackSchema = z.object({
    page_id: z.number(),
    rating: z.boolean(),
    comment: z.string().optional(),
});

// Query parameters schema for list endpoint
const listQuerySchema = z.object({
    page: z.string().optional().default("1").transform((val) => parseInt(val)),
    limit: z.string().optional().default("10").transform((val) =>
        parseInt(val)
    ),
    page_id: z.string().optional().transform((val) =>
        val ? parseInt(val) : undefined
    ),
});

export async function POST(request: Request) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const userId = userData.user.id;

        const body = await request.json();
        const result = feedbackSchema.safeParse(body);

        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid feedback data",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { data: feedback, error } = await supabaseAdmin
            .from("feedback")
            .insert({
                ...result.data,
                user_id: userId,
            })
            .select()
            .single();

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to create feedback" },
                { status: 500 },
            );
        }

        return NextResponse.json(feedback, { status: 201 });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function GET(request: Request) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const userId = userData.user.id;

        const url = new URL(request.url);
        const queryParams = {
            page: url.searchParams.get("page") || "1",
            limit: url.searchParams.get("limit") || "10",
            page_id: url.searchParams.get("page_id") || undefined,
        };

        const result = listQuerySchema.safeParse(queryParams);
        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid query parameters",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { page, limit, page_id } = result.data;
        const offset = (page - 1) * limit;

        let query = supabase
            .from("feedback")
            .select("*", { count: "exact" })
            .eq("user_id", userId) // Only return the user's own feedback
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1);

        if (page_id) {
            query = query.eq("page_id", page_id);
        }

        const { data: feedbacks, error, count } = await query;

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to fetch feedback" },
                { status: 500 },
            );
        }

        return NextResponse.json({
            items: feedbacks || [],
            total: count || 0,
            page,
            limit,
        }, {
            status: 200,
            headers: {
                "Cache-Control": "public, max-age=60",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
