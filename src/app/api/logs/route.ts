import { NextResponse } from "next/server";
import { supabase } from "@/db/supabase";
import { createClient } from "@/lib/supabase/server";
import { z } from "zod";

export const runtime = "edge";

// Query parameters schema for list endpoint
const listQuerySchema = z.object({
    page: z.string().optional().default("1").transform((val) => parseInt(val)),
    limit: z.string().optional().default("10").transform((val) =>
        parseInt(val)
    ),
    status: z.enum(["PENDING", "SUCCESS", "FAILED", "ALL"]).optional().default(
        "ALL",
    ),
    page_id: z.string().optional().transform((val) =>
        val ? parseInt(val) : undefined
    ),
});

export async function GET(request: Request) {
    try {
        // Verify authentication
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const userId = userData.user.id;

        const url = new URL(request.url);
        const queryParams = {
            page: url.searchParams.get("page") ?? "1",
            limit: url.searchParams.get("limit") ?? "10",
            status: url.searchParams.get("status") ?? "ALL",
            page_id: url.searchParams.get("page_id") ?? undefined,
        };

        const result = listQuerySchema.safeParse(queryParams);
        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid query parameters",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { page, limit, status, page_id } = result.data;
        const offset = (page - 1) * limit;

        // Define columns to select, including cost-related fields
        const columns = `
            id, page_id, status, generation_duration_ms, 
            error_message, created_at, user_id,
            prompt_tokens, completion_tokens, total_tokens,
            prompt_cost, completion_cost, total_cost, cost_currency
        `;

        let query = supabase
            .from("logs")
            .select(columns, { count: "exact" })
            .eq("user_id", userId) // Only return the user's own logs
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1);

        if (status && status !== "ALL") {
            query = query.eq("status", status);
        }

        if (page_id) {
            query = query.eq("page_id", page_id);
        }

        const { data: logs, error, count } = await query;

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to fetch logs" },
                { status: 500 },
            );
        }

        return NextResponse.json({
            items: logs ?? [],
            total: count ?? 0,
            page,
            limit,
        }, {
            status: 200,
            headers: {
                "Cache-Control": "public, max-age=60",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function PATCH(request: Request) {
    try {
        // Extract user ID from authorization header
        const authHeader = request.headers.get("authorization");
        if (!authHeader?.startsWith("Bearer ")) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix

        // Verify the token and get user
        const { data: userData, error: verifyError } = await supabase.auth
            .getUser(token);

        if (verifyError || !userData?.user) {
            return NextResponse.json(
                { error: "Invalid token" },
                { status: 401 },
            );
        }

        const userId = userData.user.id;

        // Get logs with model information for the user
        const { data: logsData, error: logsError } = await supabase
            .from("logs")
            .select(`
                total_cost, created_at,
                prompt_tokens, completion_tokens, total_tokens,
                model, generation_attempts, cumulative_tokens, cumulative_cost
            `)
            .eq("user_id", userId)
            .eq("status", "SUCCESS")
            .order("created_at", { ascending: false });

        if (logsError) {
            console.error("Database error:", logsError);
            return NextResponse.json(
                { error: "Failed to fetch cost data" },
                { status: 500 },
            );
        }

        // Calculate various cost metrics
        const totalCost = logsData.reduce(
            (sum, log) => sum + (log.total_cost ?? 0),
            0,
        );

        // Calculate cumulative costs
        const totalCumulativeCost = logsData.reduce(
            (sum, log) => sum + (log.cumulative_cost ?? log.total_cost ?? 0),
            0,
        );

        // Calculate total attempts
        const totalAttempts = logsData.reduce(
            (sum, log) => sum + (log.generation_attempts ?? 1),
            0,
        );

        // Calculate average attempts per request
        const avgAttemptsPerRequest = logsData.length > 0
            ? totalAttempts / logsData.length
            : 1;

        // Calculate savings percentage (how much would have been spent without retries)
        const savingsPercentage = totalCumulativeCost > 0 && totalCost > 0
            ? ((totalCumulativeCost - totalCost) / totalCumulativeCost) * 100
            : 0;

        // Calculate cost for last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const last30DaysCost = logsData
            .filter((log) => new Date(log.created_at) >= thirtyDaysAgo)
            .reduce((sum, log) => sum + (log.total_cost ?? 0), 0);

        // Calculate cost for current month
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        const currentMonthCost = logsData
            .filter((log) => {
                const logDate = new Date(log.created_at);
                return logDate.getMonth() === currentMonth &&
                    logDate.getFullYear() === currentYear;
            })
            .reduce((sum, log) => sum + (log.total_cost ?? 0), 0);

        // Calculate cost by model
        const modelCosts: Record<string, {
            cost: number;
            count: number;
            tokens: number;
            attempts: number;
            cumulativeCost: number;
        }> = {};

        logsData.forEach((log) => {
            const model = log.model ?? "unknown";

            if (!modelCosts[model]) {
                modelCosts[model] = {
                    cost: 0,
                    count: 0,
                    tokens: 0,
                    attempts: 0,
                    cumulativeCost: 0,
                };
            }

            modelCosts[model].cost += log.total_cost ?? 0;
            modelCosts[model].count += 1;
            modelCosts[model].tokens += log.total_tokens ?? 0;
            modelCosts[model].attempts += log.generation_attempts ?? 1;
            modelCosts[model].cumulativeCost += log.cumulative_cost ??
                log.total_cost ?? 0;
        });

        // Sort models by cost
        const modelBreakdown = Object.entries(modelCosts).map((
            [model, data],
        ) => ({
            model,
            cost: data.cost,
            count: data.count,
            tokens: data.tokens,
            attempts: data.attempts,
            avgAttemptsPerRequest: data.count > 0
                ? data.attempts / data.count
                : 1,
            cumulativeCost: data.cumulativeCost,
            avgCostPerRequest: data.count > 0 ? data.cost / data.count : 0,
        })).sort((a, b) => b.cost - a.cost);

        return NextResponse.json({
            totalCost,
            totalCumulativeCost,
            totalAttempts,
            avgAttemptsPerRequest,
            savingsPercentage,
            last30DaysCost,
            currentMonthCost,
            currency: "USD",
            modelBreakdown,
            requestCount: logsData.length,
            totalTokens: logsData.reduce(
                (sum, log) => sum + (log.total_tokens ?? 0),
                0,
            ),
        }, {
            status: 200,
            headers: {
                "Cache-Control": "private, max-age=60",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
