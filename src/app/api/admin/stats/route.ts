import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireCurrentUserAdmin } from "@/lib/auth/roles";
import { getPlatformPricePerPage } from "@/lib/constants/pricing";

// Helper functions for safe number validation
const safeNumber = (value: unknown, fallback = 0): number => {
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? fallback : Math.max(0, num);
};

const validateFinancialValue = (value: number, fallback = 0): number => {
    return isNaN(value) || !isFinite(value) || value < 0 ? fallback : value;
};

export async function GET() {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.auth.getUser();

        if (error || !data?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        // Require admin role
        try {
            await requireCurrentUserAdmin();
        } catch {
            return NextResponse.json(
                { error: "Admin access required" },
                { status: 403 },
            );
        }

        // Fetch admin statistics
        const [pagesResult, logsResult, feedbackResult] = await Promise.all([
            supabase.from("pages").select("*", { count: "exact", head: true }),
            supabase.from("logs").select("*", { count: "exact", head: true }),
            supabase.from("feedback").select("*", {
                count: "exact",
                head: true,
            }),
        ]);

        // Calculate total costs and revenue with validation
        const { data: costData } = await supabase
            .from("logs")
            .select("total_cost")
            .eq("status", "SUCCESS");

        // Safely calculate total real cost with validation
        const rawTotalRealCost = costData?.reduce((sum, log) => {
            const cost = safeNumber(log.total_cost, 0);
            return sum + cost;
        }, 0) || 0;

        const totalRealCost = validateFinancialValue(rawTotalRealCost);
        const totalPages = safeNumber(pagesResult.count, 0);
        const PLATFORM_PRICE_PER_PAGE = getPlatformPricePerPage();

        // Calculate platform revenue with validation
        const rawTotalPlatformRevenue = totalPages * PLATFORM_PRICE_PER_PAGE;
        const totalPlatformRevenue = validateFinancialValue(
            rawTotalPlatformRevenue,
        );

        // Calculate margin with validation
        const rawTotalMargin = totalPlatformRevenue - totalRealCost;
        const totalMargin = validateFinancialValue(rawTotalMargin);

        // Calculate average margin per page with safe division
        const averageMarginPerPage = totalPages > 0
            ? validateFinancialValue(totalMargin / totalPages)
            : 0;

        return NextResponse.json({
            totalUsers: 0, // We'll implement user count later
            totalPages: pagesResult.count || 0,
            totalLogs: logsResult.count || 0,
            totalFeedback: feedbackResult.count || 0,
            totalRealCost,
            totalPlatformRevenue,
            totalMargin,
            averageMarginPerPage,
        });
    } catch (error) {
        console.error("Error fetching admin stats:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
