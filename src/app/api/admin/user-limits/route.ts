import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { requireCurrentUserAdmin } from "@/lib/auth/roles";
import { UserLimitsService } from "@/lib/user-limits";
import { z } from "zod";

const updateLimitSchema = z.object({
    userId: z.string().uuid(),
    dailyLimit: z.number().int().min(0).max(1000),
});

const resetUsageSchema = z.object({
    userId: z.string().uuid(),
});

export async function POST(request: Request) {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.auth.getUser();

        if (error || !data?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        // Require admin role
        try {
            await requireCurrentUserAdmin();
        } catch {
            return NextResponse.json(
                { error: "Admin access required" },
                { status: 403 },
            );
        }

        const body = await request.json();
        const result = updateLimitSchema.safeParse(body);

        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid request data",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { userId, dailyLimit } = result.data;
        const userLimitsService = UserLimitsService.getInstance();

        const success = await userLimitsService.setUserDailyLimit(
            userId,
            dailyLimit,
        );

        if (!success) {
            return NextResponse.json(
                { error: "Failed to update user daily limit" },
                { status: 500 },
            );
        }

        return NextResponse.json({
            message: "User daily limit updated successfully",
            userId,
            dailyLimit,
        });
    } catch (error) {
        console.error("Error updating user daily limit:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function GET(request: Request) {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.auth.getUser();

        if (error || !data?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        // Require admin role
        try {
            await requireCurrentUserAdmin();
        } catch {
            return NextResponse.json(
                { error: "Admin access required" },
                { status: 403 },
            );
        }

        const url = new URL(request.url);
        const userId = url.searchParams.get("userId");

        if (!userId) {
            return NextResponse.json(
                { error: "userId parameter is required" },
                { status: 400 },
            );
        }

        const userLimitsService = UserLimitsService.getInstance();
        const userLimits = await userLimitsService.getUserLimits(userId);

        return NextResponse.json(userLimits);
    } catch (error) {
        console.error("Error fetching user limits:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}

export async function DELETE(request: Request) {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.auth.getUser();

        if (error || !data?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        // Require admin role
        try {
            await requireCurrentUserAdmin();
        } catch {
            return NextResponse.json(
                { error: "Admin access required" },
                { status: 403 },
            );
        }

        const body = await request.json();
        const result = resetUsageSchema.safeParse(body);

        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid request data",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { userId } = result.data;
        const userLimitsService = UserLimitsService.getInstance();

        const success = await userLimitsService.resetDailyUsage(userId);

        if (!success) {
            return NextResponse.json(
                { error: "Failed to reset user daily usage" },
                { status: 500 },
            );
        }

        return NextResponse.json({
            message: "User daily usage reset successfully",
            userId,
        });
    } catch (error) {
        console.error("Error resetting user daily usage:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
