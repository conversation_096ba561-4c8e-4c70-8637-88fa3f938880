import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { supabaseAdmin } from "@/db/supabase";
import { requireCurrentUserAdmin } from "@/lib/auth/roles";

export async function GET() {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.auth.getUser();

        if (error || !data?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        // Require admin role
        try {
            await requireCurrentUserAdmin();
        } catch {
            return NextResponse.json(
                { error: "Admin access required" },
                { status: 403 },
            );
        }

        // Use admin client to list users
        const { data: { users }, error: usersError } = await supabaseAdmin.auth
            .admin.listUsers();

        if (usersError) {
            console.error("Error fetching users:", usersError);
            return NextResponse.json(
                { error: "Failed to fetch users" },
                { status: 500 },
            );
        }

        // Return users with relevant information
        const userList = users.map((user) => ({
            id: user.id,
            email: user.email || "No email",
            daily_page_limit: user.user_metadata?.daily_page_limit || 1,
            created_at: user.created_at,
        }));

        return NextResponse.json({ users: userList });
    } catch (error) {
        console.error("Error in admin users endpoint:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
