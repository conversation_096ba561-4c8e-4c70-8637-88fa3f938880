import { z } from "zod";
import { NextResponse } from "next/server";
import { AdminPageListItemResponse, AdminPageListResponse } from "@/types";
import { supabase } from "@/db/supabase";
import type { Database } from "@/db/database.types";
import { createClient } from "@/lib/supabase/server";
import { requireCurrentUserAdmin } from "@/lib/auth/roles";

export const runtime = "edge";

// Query parameters schema for list endpoint
const listQuerySchema = z.object({
    page: z.string().optional().default("1").transform((val) => parseInt(val)),
    limit: z.string().optional().default("20").transform((val) =>
        parseInt(val)
    ),
    status: z.enum(["PENDING", "SUCCESS", "FAILED", "ALL"]).optional().default(
        "ALL",
    ),
});

export async function GET(request: Request) {
    try {
        // Get authenticated user and verify admin role
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            return NextResponse.json(
                { error: "Unauthorized - You must be logged in." },
                { status: 401 },
            );
        }

        // Check if user is admin
        try {
            await requireCurrentUserAdmin();
        } catch {
            return NextResponse.json(
                { error: "Forbidden - Admin access required." },
                { status: 403 },
            );
        }

        const url = new URL(request.url);
        const queryParams = {
            page: url.searchParams.get("page") ?? "1",
            limit: url.searchParams.get("limit") ?? "20",
            status: url.searchParams.get("status") ?? "ALL",
        };

        const result = listQuerySchema.safeParse(queryParams);
        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid query parameters",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { page, limit, status } = result.data;
        const offset = (page - 1) * limit;

        // Get all pages (no user filtering for admin)
        let query = supabase
            .from("pages")
            .select("*", { count: "exact" })
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1);

        if (status && status !== "ALL") {
            query = query.eq("status", status);
        }

        const { data: pages, error, count } = await query;

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to fetch pages" },
                { status: 500 },
            );
        }

        // If we have pages, fetch their logs to get cost information
        const logData: Record<number, {
            total_cost: number | null;
            cost_currency: string | null;
            prompt_tokens: number | null;
            completion_tokens: number | null;
            total_tokens: number | null;
            model: string | null;
            generation_attempts: number | null;
            generation_duration_ms: number | null;
            status: string | null;
        }> = {};

        if (pages && pages.length > 0) {
            const pageIds = pages.map((page) => page.id);

            // Get the latest log for each page that has cost information
            const { data: logs, error: logsError } = await supabase
                .from("logs")
                .select(
                    "page_id, total_cost, cost_currency, prompt_tokens, completion_tokens, total_tokens, model, generation_attempts, generation_duration_ms, status",
                )
                .in("page_id", pageIds)
                .order("created_at", { ascending: false });

            if (logsError) {
                console.error("Error fetching logs:", logsError);
                // Continue without cost data if logs fetch fails
            } else if (logs) {
                // Create a map of page_id to log data, keeping only the first (latest) log for each page
                logs.forEach((log) => {
                    if (!logData[log.page_id]) {
                        logData[log.page_id] = {
                            total_cost: log.total_cost,
                            cost_currency: log.cost_currency,
                            prompt_tokens: log.prompt_tokens,
                            completion_tokens: log.completion_tokens,
                            total_tokens: log.total_tokens,
                            model: log.model,
                            generation_attempts: log.generation_attempts,
                            generation_duration_ms: log.generation_duration_ms,
                            status: log.status,
                        };
                    }
                });
            }
        }

        const items: AdminPageListItemResponse[] = (pages ?? []).map((page) => {
            const logInfo = logData[page.id];

            return {
                id: page.id,
                config: page
                    .config as unknown as AdminPageListItemResponse["config"],
                created_at: page.created_at,
                status:
                    (page.status ?? logInfo?.status ?? "PENDING") as Database[
                        "public"
                    ]["Enums"]["generation_status"],
                user_id: page.user_id, // Include user_id for admin view
                // Include cost information if available
                ...(logInfo && {
                    cost: {
                        total: logInfo.total_cost,
                        currency: logInfo.cost_currency,
                    },
                    tokens: {
                        prompt: logInfo.prompt_tokens,
                        completion: logInfo.completion_tokens,
                        total: logInfo.total_tokens,
                    },
                    model: logInfo.model,
                    generation_attempts: logInfo.generation_attempts,
                    generation_duration_ms: logInfo.generation_duration_ms,
                }),
            };
        });

        const response: AdminPageListResponse = {
            items,
            total: count ?? 0,
            page,
            limit,
        };

        return NextResponse.json(response, {
            status: 200,
            headers: {
                "Cache-Control": "no-store",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
