import { z } from "zod";
import { NextResponse } from "next/server";
import {
    CreatePageCommand,
    CreatePageResponse,
    PageListItemResponse,
    PageListResponse,
} from "@/types";
import { supabase } from "@/db/supabase";
import type { Database } from "@/db/database.types";
import { createClient } from "@/lib/supabase/server";

export const runtime = "edge";

// Zod validation schema for PageConfig
const pageConfigSchema = z.object({
    type: z.string().min(1),
    targetAudience: z.string().min(1),
    keywords: z.array(z.string()),
    style: z.string().min(1),
    language: z.string().min(2),
    integrations: z.object({
        leadCapture: z.string(),
    }).optional(),
});

// Validation schema for the entire request body
const createPageCommandSchema = z.object({
    config: pageConfigSchema,
    html_content: z.string().optional().default(""), // For revisions
    parent_page_id: z.number().optional(), // For creating revisions
});

// Query parameters schema for list endpoint
const listQuerySchema = z.object({
    page: z.string().optional().default("1").transform((val) => parseInt(val)),
    limit: z.string().optional().default("10").transform((val) =>
        parseInt(val)
    ),
    status: z.enum(["PENDING", "SUCCESS", "FAILED", "ALL"]).optional().default(
        "ALL",
    ),
});

export async function POST(request: Request) {
    let userId: string | undefined = undefined;
    try {
        // Parse and validate request body
        const body = await request.json();
        const result = createPageCommandSchema.safeParse(body);

        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid request data",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const command = result.data as CreatePageCommand;

        // Get authenticated user
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            console.error(
                "Authentication error in POST /api/pages:",
                authError,
            );
            return NextResponse.json(
                {
                    error:
                        "Unauthorized - You must be logged in to create a page.",
                },
                { status: 401 },
            );
        }

        userId = userData.user.id;

        // Insert page record using the authenticated client
        const { data: page, error: pageError } = await client
            .from("pages")
            .insert({
                config: command
                    .config as unknown as Database["public"]["Tables"]["pages"][
                        "Insert"
                    ]["config"],
                html_content: command.html_content ?? "",
                parent_page_id: command.parent_page_id ?? null,
                user_id: userId,
            })
            .select()
            .single();

        if (pageError) {
            console.error("Database error:", {
                error: pageError,
                message: pageError.message,
                details: pageError.details,
                hint: pageError.hint,
                code: pageError.code,
            });

            // Log error
            await supabase.from("logs").insert({
                page_id: 0,
                status: "FAILED" as Database["public"]["Enums"][
                    "generation_status"
                ],
                error_message: `Failed to create page: ${pageError.message}`,
                user_id: userId,
            });

            return NextResponse.json(
                {
                    error: "Failed to create page",
                    details: {
                        message: pageError.message,
                        code: pageError.code,
                    },
                },
                { status: 500 },
            );
        }

        if (!page) {
            throw new Error("Page creation failed but no error was returned");
        }

        // Log success
        await supabase.from("logs").insert({
            page_id: page.id,
            status:
                "SUCCESS" as Database["public"]["Enums"]["generation_status"],
            user_id: userId,
        });

        const response: CreatePageResponse = {
            id: page.id,
            config: command.config,
            status: "PENDING",
            created_at: page.created_at,
        };

        return NextResponse.json(response, { status: 201 });
    } catch (error) {
        console.error("Unexpected error:", error);

        // Log unexpected error
        await supabase.from("logs").insert({
            page_id: 0,
            status:
                "FAILED" as Database["public"]["Enums"]["generation_status"],
            error_message: `Unexpected error creating page: ${
                error instanceof Error ? error.message : "Unknown error"
            }`,
            user_id: userId ?? "unknown-user",
        });

        return NextResponse.json(
            {
                error: "Internal server error",
                details: error instanceof Error
                    ? error.message
                    : "Unknown error",
            },
            { status: 500 },
        );
    }
}

export async function GET(request: Request) {
    try {
        // Get authenticated user
        const client = await createClient();
        const { data: userData, error: authError } = await client.auth
            .getUser();

        if (authError || !userData?.user) {
            console.error(
                "Authentication error in GET /api/pages:",
                authError,
            );
            return NextResponse.json(
                {
                    error:
                        "Unauthorized - You must be logged in to view pages.",
                },
                { status: 401 },
            );
        }

        const userId = userData.user.id;

        const url = new URL(request.url);
        const queryParams = {
            page: url.searchParams.get("page") ?? "1",
            limit: url.searchParams.get("limit") ?? "10",
            status: url.searchParams.get("status") ?? "ALL",
        };

        const result = listQuerySchema.safeParse(queryParams);
        if (!result.success) {
            return NextResponse.json(
                {
                    error: "Invalid query parameters",
                    details: result.error.format(),
                },
                { status: 400 },
            );
        }

        const { page, limit, status } = result.data;
        const offset = (page - 1) * limit;

        // First get the pages
        let query = supabase
            .from("pages")
            .select("*", { count: "exact" })
            .eq("user_id", userId) // Only return the user's own pages
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1);

        if (status && status !== "ALL") {
            query = query.eq("status", status);
        }

        const { data: pages, error, count } = await query;

        if (error) {
            console.error("Database error:", error);
            return NextResponse.json(
                { error: "Failed to fetch pages" },
                { status: 500 },
            );
        }

        // If we have pages, fetch their logs to get cost information
        const logData: Record<number, {
            total_cost: number | null;
            cost_currency: string | null;
            prompt_tokens: number | null;
            completion_tokens: number | null;
            total_tokens: number | null;
            model: string | null;
            generation_attempts: number | null;
            generation_duration_ms: number | null;
            status: string | null;
        }> = {};

        if (pages && pages.length > 0) {
            const pageIds = pages.map((page) => page.id);

            // Get the latest log for each page that has cost information
            const { data: logs, error: logsError } = await supabase
                .from("logs")
                .select(
                    "page_id, total_cost, cost_currency, prompt_tokens, completion_tokens, total_tokens, model, generation_attempts, generation_duration_ms, status",
                )
                .in("page_id", pageIds)
                .order("created_at", { ascending: false });

            if (logsError) {
                console.error("Error fetching logs:", logsError);
                // Continue without cost data if logs fetch fails
            } else if (logs) {
                // Create a map of page_id to log data, keeping only the first (latest) log for each page
                logs.forEach((log) => {
                    if (!logData[log.page_id]) {
                        logData[log.page_id] = {
                            total_cost: log.total_cost,
                            cost_currency: log.cost_currency,
                            prompt_tokens: log.prompt_tokens,
                            completion_tokens: log.completion_tokens,
                            total_tokens: log.total_tokens,
                            model: log.model,
                            generation_attempts: log.generation_attempts,
                            generation_duration_ms: log.generation_duration_ms,
                            status: log.status,
                        };
                    }
                });
            }
        }

        const items: PageListItemResponse[] = (pages ?? []).map((page) => {
            const logInfo = logData[page.id];

            return {
                id: page.id,
                config: page
                    .config as unknown as PageListItemResponse["config"],
                created_at: page.created_at,
                status:
                    (page.status ?? logInfo?.status ?? "PENDING") as Database[
                        "public"
                    ]["Enums"]["generation_status"],
                // Include cost information if available
                ...(logInfo && {
                    cost: {
                        total: logInfo.total_cost,
                        currency: logInfo.cost_currency,
                    },
                    tokens: {
                        prompt: logInfo.prompt_tokens,
                        completion: logInfo.completion_tokens,
                        total: logInfo.total_tokens,
                    },
                    model: logInfo.model,
                    generation_attempts: logInfo.generation_attempts,
                    generation_duration_ms: logInfo.generation_duration_ms,
                }),
            };
        });

        const response: PageListResponse = {
            items,
            total: count ?? 0,
            page,
            limit,
        };

        return NextResponse.json(response, {
            status: 200,
            headers: {
                "Cache-Control": "no-store",
            },
        });
    } catch (error) {
        console.error("Unexpected error:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
