import { ResetPasswordForm } from "@/components/auth/forms/ResetPasswordForm";

export const runtime = "edge";

// Reset Password page - renders the ResetPasswordForm component
export default function ResetPasswordPage() {
  // Note: The logic to handle the reset token from the URL
  // is implicitly handled by Supabase Auth Helpers when the page loads
  // and the server action `resetPassword` is called.
  return <ResetPasswordForm />;
}
