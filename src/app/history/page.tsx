import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import PageHistory from "@/components/dashboard/PageHistory";
import Link from "next/link";
import { RectangleGroupIcon } from "@heroicons/react/24/outline";

export const runtime = "edge";

export default async function HistoryPage() {
  // Ensure user is authenticated
  const supabase = await createClient();
  const { data, error } = await supabase.auth.getUser();

  if (error || !data?.user) {
    redirect("/login");
  }

  return (
    <main className="container pb-10 mx-auto">
      <section className="flex flex-col items-center space-y-6 mb-8 pt-12">
        <div className="text-center mb-6">
          <h1
            className="text-3xl font-bold tracking-tight"
            data-testid="history-heading"
          >
            Generated Pages History
          </h1>
          <p className="text-muted-foreground mt-2">
            View and manage all your previously generated landing pages
          </p>
        </div>
        <div className="flex justify-center">
          <Link
            href="/dashboard"
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary border border-primary rounded-md hover:bg-primary/10 transition-colors"
          >
            <RectangleGroupIcon className="w-4 h-4" />
            Back to Dashboard
          </Link>
        </div>
      </section>

      <section className="w-full mx-auto">
        <PageHistory />
      </section>
    </main>
  );
}
