import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { requireCurrentUserAdmin } from "@/lib/auth/roles";
import { AdminDashboard } from "@/components/admin/AdminDashboard";

export default async function AdminPage() {
  const supabase = await createClient();
  const { data, error } = await supabase.auth.getUser();

  if (error || !data?.user) {
    redirect("/login");
  }

  try {
    await requireCurrentUserAdmin();
  } catch {
    redirect("/dashboard");
  }

  return <AdminDashboard />;
}
