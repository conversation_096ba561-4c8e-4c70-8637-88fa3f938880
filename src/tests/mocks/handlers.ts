import { http, HttpResponse } from "msw";

// Definicja handlerów dla mockowania zapytań API
export const handlers = [
    // Przykład mockowania API Supabase
    http.get("*/rest/v1/*", () => {
        return HttpResponse.json([]);
    }),

    // Unsplash API handlers
    http.get("https://api.unsplash.com/search/photos", ({ request }) => {
        const url = new URL(request.url);
        const query = url.searchParams.get("query");

        if (query === "unauthorized") {
            return new HttpResponse(null, {
                status: 401,
                statusText: "Unauthorized",
            });
        }

        if (query === "test") {
            return HttpResponse.json({
                total: 1,
                total_pages: 1,
                results: [
                    {
                        urls: {
                            regular: "test-image-url",
                        },
                    },
                ],
            });
        }

        // For "nonexistent" and any other query, return empty results
        return HttpResponse.json({
            total: 0,
            total_pages: 0,
            results: [],
        });
    }),

    // Image validation handlers
    http.head("https://valid.com/image.jpg", () => {
        return new HttpResponse(null, { status: 200 });
    }),

    http.head("https://invalid.com/image.jpg", () => {
        return new HttpResponse(null, { status: 404 });
    }),

    http.head("https://example.com/image.jpg", () => {
        return new HttpResponse(null, { status: 200 });
    }),
];
