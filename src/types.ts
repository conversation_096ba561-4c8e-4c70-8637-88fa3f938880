import { Database } from "./db/database.types";

// Enums
export type GenerationStatus = Database["public"]["Enums"]["generation_status"];

// User roles
export type UserRole = "admin" | "subscriber";

export interface UserWithRole {
    id: string;
    email: string | undefined;
    role: UserRole;
}

// Page Config Types
export interface PageConfig {
    type: string;
    targetAudience: string;
    keywords: string[];
    style: string;
    language: string;
    integrations: {
        leadCapture: string;
    };
}

// Command Models (Request DTOs)
export interface CreatePageCommand {
    config: PageConfig;
    html_content?: string; // For revisions
    parent_page_id?: number; // For creating revisions
}

export interface UploadAssetCommand {
    file: File;
    type: "logo" | "image";
}

export interface SubmitFeedbackCommand {
    rating: boolean;
    comment?: string;
}

// Response DTOs
export interface CreatePageResponse {
    id: number;
    config: PageConfig;
    status: GenerationStatus;
    created_at: string;
    html?: string;
    isPartial?: boolean;
}

export interface UploadAssetResponse {
    url: string;
    type: string;
}

export interface GeneratePageResponse {
    id: number;
    status: GenerationStatus;
    estimatedTime: number;
}

export interface PageResponse {
    id: number;
    config: PageConfig;
    html_content: string;
    created_at: string;
    parent_page_id: number | null;
    status: GenerationStatus;
}

export interface PageListItemResponse {
    id: number;
    config: PageConfig;
    created_at: string;
    status: GenerationStatus;
    cost?: {
        total: number | null;
        currency: string | null;
    };
    tokens?: {
        prompt: number | null;
        completion: number | null;
        total: number | null;
    };
    model?: string | null;
    generation_attempts?: number | null;
    generation_duration_ms?: number | null;
}

export interface AdminPageListItemResponse extends PageListItemResponse {
    user_id: string;
}

export interface PageListResponse {
    items: PageListItemResponse[];
    total: number;
    page: number;
    limit: number;
}

export interface AdminPageListResponse {
    items: AdminPageListItemResponse[];
    total: number;
    page: number;
    limit: number;
}

export interface FeedbackResponse {
    id: string;
    created_at: string;
}

export interface GenerationStatsResponse {
    generationsToday: number;
    generationsRemaining: number;
    resetTime: string;
}

// Utility type to extract specific page fields from database
export type PageRecord = Database["public"]["Tables"]["pages"]["Row"];

// Utility type to extract specific feedback fields from database
export type FeedbackRecord = Database["public"]["Tables"]["feedback"]["Row"];

// Utility type to extract specific logs fields from database
export type LogRecord = Database["public"]["Tables"]["logs"]["Row"];
