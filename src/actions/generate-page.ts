"use server";

import { z } from "zod";
import { supabase } from "@/db/supabase";
import {
    createPageGeneratorService,
    PageStyle,
    PageType,
} from "@/services/page-generator";
import { Database } from "@/db/database.types";
import { CreatePageResponse, PageConfig } from "@/types";
import { createClient } from "@/lib/supabase/server";
import type { SupabaseClient } from "@supabase/supabase-js";
import { imageValidator } from "@/services/image-validator";

/**
 * Schema for validating the form data
 */
const generatePageSchema = z.object({
    productDescription: z.string().min(
        3,
        "Product name (from product description field) must be at least 3 characters",
    ),
    targetAudience: z.string().min(
        5,
        "Target audience must be at least 5 characters",
    ),
    keywords: z.string().min(3, "Keywords must be at least 3 characters"),
    type: z.nativeEnum(PageType),
    style: z.nativeEnum(PageStyle),
    language: z.string().min(2, "Language must be at least 2 characters"),
    additionalRequirements: z.string().optional(),
    leadCaptureCode: z.string().optional(),
    integrationCode: z.string().optional(),
});

/**
 * Type for the form data
 */
export type GeneratePageFormData = z.infer<typeof generatePageSchema>;

/**
 * Type for the response
 */
export type GeneratePageActionResponse = {
    success: boolean;
    message?: string;
    errors?: Record<string, string[]>;
    data?: CreatePageResponse;
};

function validateGeneratePageFormData(formData: GeneratePageFormData):
    | { success: false; errors: Record<string, string[]>; message: string }
    | { success: true; data: GeneratePageFormData } {
    const validationResult = generatePageSchema.safeParse(formData);
    if (!validationResult.success) {
        const formattedErrors: Record<string, string[]> = {};
        const errors = validationResult.error.format();
        Object.entries(errors).forEach(([key, value]) => {
            if (
                key !== "_errors" && typeof value === "object" &&
                "_errors" in value
            ) {
                formattedErrors[key] = value._errors;
            }
        });
        return {
            success: false,
            errors: formattedErrors,
            message: "Validation failed",
        };
    }
    return { success: true, data: validationResult.data };
}

async function insertPageOrLogError(
    supabase: SupabaseClient,
    pageConfig: PageConfig,
    userId: string,
): Promise<
    { success: true; page: { id: number; created_at: string } } | {
        success: false;
        message: string;
    }
> {
    const { data: page, error: pageError } = await supabase
        .from("pages")
        .insert({
            config:
                pageConfig as unknown as Database["public"]["Tables"]["pages"][
                    "Insert"
                ]["config"],
            html_content: "",
            user_id: userId,
        })
        .select()
        .single();
    if (pageError) {
        await supabase.from("logs").insert({
            page_id: 0,
            status:
                "FAILED" as Database["public"]["Enums"]["generation_status"],
            error_message: `Failed to create page: ${pageError.message}`,
            user_id: userId,
        });
        return {
            success: false,
            message: `Failed to create page record: ${pageError.message}`,
        };
    }
    return { success: true, page };
}

interface GenerateAndLogPageContentParams {
    pageGenerator: ReturnType<typeof createPageGeneratorService>;
    page: { id: number; created_at: string };
    supabase: SupabaseClient;
    userId: string;
    productDescription: string;
    targetAudience: string;
    keywordsArray: string[];
    type: PageConfig["type"];
    style: PageConfig["style"];
    language: PageConfig["language"];
    additionalRequirements?: string;
    integrationCode?: string;
    pageConfig: PageConfig;
}

async function generateAndLogPageContent({
    pageGenerator,
    page,
    supabase,
    userId,
    productDescription,
    targetAudience,
    keywordsArray,
    type,
    style,
    language,
    additionalRequirements,
    integrationCode,
    pageConfig,
}: GenerateAndLogPageContentParams): Promise<GeneratePageActionResponse> {
    const result = await pageGenerator.generatePage({
        productName: productDescription,
        productDescription: productDescription,
        targetAudience,
        keywords: keywordsArray,
        type: type as PageType,
        style: style as PageStyle,
        language: language,
        additionalRequirements: additionalRequirements ?? "",
        integrationCode: integrationCode ?? "",
    });

    // Validate and fix images in the generated HTML
    let finalHtml = result.html;
    try {
        const validationResult = await imageValidator.validateAndFixHtml(
            result.html,
        );
        finalHtml = validationResult.html;

        if (validationResult.summary.replacedImages > 0) {
            console.log(
                `[Image Validation] Replaced ${validationResult.summary.replacedImages} invalid images for page ${page.id}`,
            );
        }
    } catch (imageError) {
        console.error(
            "[Image Validation] Failed to validate images:",
            imageError,
        );
        // Continue with original HTML if validation fails
    }

    const { error: updateError } = await supabase
        .from("pages")
        .update({ html_content: finalHtml })
        .eq("id", page.id);
    if (updateError) {
        throw new Error(`Failed to update page: ${updateError.message}`);
    }
    const { error: logError } = await supabase.from("logs").insert({
        page_id: page.id,
        status: "SUCCESS" as Database["public"]["Enums"]["generation_status"],
        generation_duration_ms: result.generationDurationMs,
        user_id: userId,
        prompt_tokens: result.tokens?.prompt ?? 0,
        completion_tokens: result.tokens?.completion ?? 0,
        total_tokens: result.tokens?.total ?? 0,
        prompt_cost: result.cost?.promptCost ?? 0,
        completion_cost: result.cost?.completionCost ?? 0,
        total_cost: result.cost?.totalCost ?? 0,
        cost_currency: result.cost?.currency ?? "USD",
        model: result.model ?? "unknown",
        generation_attempts: result.totalAttempts ?? 1,
        cumulative_tokens: result.cumulativeCost?.totalTokens ??
            result.tokens?.total ?? 0,
        cumulative_cost: result.cumulativeCost?.totalCost ??
            result.cost?.totalCost ?? 0,
    });
    if (logError) {
        console.error("[DEBUG] Failed to log success:", logError);
    }
    return {
        success: true,
        data: {
            id: page.id,
            config: pageConfig,
            status: "SUCCESS",
            created_at: page.created_at,
            html: finalHtml,
            isPartial: false,
        },
    };
}

function isEnumValue<T>(enumObj: T, value: unknown): value is T[keyof T] {
    return Object.values(enumObj as object).includes(value as T[keyof T]);
}

/**
 * Server action for generating a landing page
 */
export async function generatePage(
    formData: GeneratePageFormData,
): Promise<GeneratePageActionResponse> {
    // Validate form data
    const validation = validateGeneratePageFormData(formData);
    if (!validation.success) {
        return {
            success: false,
            errors: validation.errors,
            message: validation.message,
        };
    }
    const {
        productDescription,
        targetAudience,
        keywords,
        type,
        style,
        language,
        additionalRequirements,
        leadCaptureCode,
        integrationCode,
    } = validation.data;
    const keywordsArray = keywords.split(",").map((k) => k.trim()).filter((k) =>
        k.length > 0
    );
    // Get current user ID from auth context
    const client = await createClient();
    const { data: userData, error: authError } = await client.auth.getUser();
    if (authError || !userData?.user) {
        return { success: false, message: "User not authenticated" };
    }
    const userId = userData.user.id;
    if (
        !isEnumValue(PageType, type) || !isEnumValue(PageStyle, style)
    ) {
        return {
            success: false,
            message: "Invalid type or style value",
        };
    }
    const pageConfig: PageConfig = {
        type: type as PageType,
        targetAudience,
        keywords: keywordsArray,
        style: style as PageStyle,
        language: language,
        integrations: { leadCapture: leadCaptureCode ?? "" },
    };
    // Insert page record first with empty html_content
    const insertResult = await insertPageOrLogError(
        supabase,
        pageConfig,
        userId,
    );
    if (!insertResult.success) return insertResult;
    const page = insertResult.page;
    try {
        const pageGenerator = createPageGeneratorService(
            process.env.OPENROUTER_API_KEY ?? "",
        );
        return await generateAndLogPageContent({
            pageGenerator,
            page,
            supabase,
            userId,
            productDescription,
            targetAudience,
            keywordsArray,
            type,
            style,
            language,
            additionalRequirements,
            integrationCode,
            pageConfig,
        });
    } catch (error) {
        // Log generation failure
        const { error: logError } = await supabase.from("logs").insert({
            page_id: page.id,
            status:
                "FAILED" as Database["public"]["Enums"]["generation_status"],
            error_message: error instanceof Error
                ? error.message
                : "Unknown error",
            user_id: userId,
        });
        if (logError) {
            console.error("[DEBUG] Failed to log error:", logError);
        }
        return {
            success: false,
            message: error instanceof Error
                ? error.message
                : "Failed to generate page",
        };
    }
}
