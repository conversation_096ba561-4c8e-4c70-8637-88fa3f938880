"use server";

import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
    AuthActionResult,
    ForgotPasswordSchema,
    LoginSchema,
    RegisterSchema,
    ResetPasswordSchema,
} from "@/lib/validators/auth";
import { z } from "zod";

export async function login(
    data: z.infer<typeof LoginSchema>,
): Promise<AuthActionResult> {
    const supabase = await createClient();

    const validatedFields = LoginSchema.safeParse(data);

    // If validation fails, return errors early
    if (!validatedFields.success) {
        return {
            success: false,
            fieldErrors: validatedFields.error.flatten().fieldErrors,
        };
    }

    const { email, password } = validatedFields.data;

    // Perform login with Supabase
    try {
        const { error } = await supabase.auth.signInWithPassword({
            email,
            password,
        });

        // If Supabase returns an error
        if (error) {
            // Log the specific error for debugging
            console.error("Supabase login error:", error.message);

            // Return a generic error message to the user
            return {
                success: false,
                message: "Invalid login credentials. Please try again.",
            };
        }

        // On successful login, Supabase handles setting the session cookie via middleware.
        // We just need to redirect.

        // Redirect to dashboard (or desired page)
        // Note: redirect() automatically throws an error, so it doesn't need a return
        redirect("/dashboard");

        // This part is technically unreachable due to redirect(), but satisfies TypeScript
        // return {
        //   success: true,
        //   redirect: '/dashboard',
        // };
    } catch (error) {
        // If the error is the specific one thrown by our redirect mock, re-throw it
        if (error instanceof Error && error.message === "NEXT_REDIRECT") {
            throw error;
        }
        console.error("Unexpected error during login:", error);
        return {
            success: false,
            message: "An unexpected error occurred. Please try again.",
        };
    }
}

// Helper function to verify the CAPTCHA token with Cloudflare
async function verifyCaptcha(token: string): Promise<boolean> {
    const secretKey = process.env.CLOUDFLARE_TURNSTILE_SECRET_KEY;

    if (!secretKey) {
        console.error("CLOUDFLARE_TURNSTILE_SECRET_KEY is not set.");
        // In production, you might want to throw an error or handle this differently
        return false; // Fail verification if secret key is missing
    }

    try {
        const response = await fetch(
            "https://challenges.cloudflare.com/turnstile/v0/siteverify",
            {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                // Cloudflare expects 'secret' and 'response' fields in the body
                body: JSON.stringify({ secret: secretKey, response: token }),
            },
        );

        if (!response.ok) {
            console.error(
                `Cloudflare verification request failed: ${response.status} ${response.statusText}`,
            );
            return false;
        }

        const data = await response.json();
        console.log("Cloudflare verification response:", data); // Optional: Log for debugging

        return data.success === true;
    } catch (error) {
        console.error("Error verifying CAPTCHA:", error);
        return false;
    }
}

export async function register(
    data: z.infer<typeof RegisterSchema>,
    captchaToken: string,
): Promise<AuthActionResult> {
    const supabase = await createClient();

    // 1. Validate form fields
    const validatedFields = RegisterSchema.safeParse(data);
    if (!validatedFields.success) {
        return {
            success: false,
            fieldErrors: validatedFields.error.flatten().fieldErrors,
        };
    }

    // 2. Verify CAPTCHA token (Server-side)
    const captchaValid = await verifyCaptcha(captchaToken);
    if (!captchaValid) {
        return {
            success: false,
            message: "CAPTCHA verification failed. Please try again.",
        };
    }

    const { email, password } = validatedFields.data;

    // Construct the redirect URL dynamically based on the environment
    const origin = process.env.NEXT_PUBLIC_APP_URL ?? "http://localhost:3000";
    const emailRedirectTo = `${origin}/auth/callback`;

    // 3. Sign up user with Supabase
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp(
        {
            email,
            password,
            options: {
                emailRedirectTo: emailRedirectTo,
            },
        },
    );

    if (signUpError) {
        console.error("Supabase sign up error:", signUpError.message);
        // Handle specific errors like email already registered
        if (signUpError.message.includes("User already registered")) {
            return {
                success: false,
                fieldErrors: { email: ["This email is already registered."] },
            };
        }
        return {
            success: false,
            message: "An error occurred during registration. Please try again.",
        };
    }

    // Check if user needs email confirmation
    if (signUpData.user && signUpData.user.identities?.length === 0) {
        // This indicates email confirmation is needed but wasn't automatically sent
        // (e.g., if auto-confirm is enabled but failed for some reason, or disabled)
        // You might want to resend confirmation here if needed, but Supabase usually handles it
        console.warn(
            "User signed up but might require manual confirmation handling.",
        );
        return {
            success: true,
            message:
                "Registration successful, but confirmation might be needed.",
        };
    }

    // If signUp is successful and email confirmation is required, Supabase sends the email.
    return {
        success: true,
        message:
            "Registration successful! Please check your email to verify your account.",
    };
}

export async function requestPasswordReset(
    data: z.infer<typeof ForgotPasswordSchema>,
): Promise<AuthActionResult> {
    const supabase = await createClient();

    // 1. Validate form fields
    const validatedFields = ForgotPasswordSchema.safeParse(data);
    if (!validatedFields.success) {
        return {
            success: false,
            fieldErrors: validatedFields.error.flatten().fieldErrors,
        };
    }

    const { email } = validatedFields.data;

    // Construct the redirect URL for the password reset confirmation page
    const origin = process.env.NEXT_PUBLIC_APP_URL ?? "http://localhost:3000";
    // IMPORTANT: This MUST match the redirect URL configured in your Supabase Auth settings
    // and the page where you handle the actual password update (e.g., /reset-password)
    const redirectTo = `${origin}/reset-password`;

    try {
        // 2. Call Supabase to send the reset email
        const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: redirectTo,
        });

        if (error) {
            console.error(
                "Supabase password reset request error:",
                error.message,
            );
            // Avoid confirming if the email exists for security reasons
            // Return a generic success message regardless of whether the email was found
            return {
                success: true, // Still return success=true to prevent email enumeration
                message:
                    "If an account with this email exists, a password reset link has been sent.",
            };
        }

        // Return generic success message
        return {
            success: true,
            message:
                "If an account with this email exists, a password reset link has been sent.",
        };
    } catch (error) {
        // Handle network errors or other exceptions
        console.error("Password reset error:", error);
        // Return the same generic message for security
        return {
            success: true, // Still return success to prevent email enumeration
            message:
                "If an account with this email exists, a password reset link has been sent.",
        };
    }
}

export async function resetPassword(
    data: z.infer<typeof ResetPasswordSchema>,
): Promise<AuthActionResult> {
    const supabase = await createClient();

    // 1. Validate form fields
    const validatedFields = ResetPasswordSchema.safeParse(data);
    if (!validatedFields.success) {
        return {
            success: false,
            fieldErrors: validatedFields.error.flatten().fieldErrors,
        };
    }

    // User should already be authenticated via the reset token link
    // which updates the session handled by the middleware.
    const { password } = validatedFields.data;

    // 2. Update the user's password
    const { error } = await supabase.auth.updateUser({
        password: password,
    });

    if (error) {
        console.error("Supabase password update error:", error.message);
        // Handle specific errors if needed, e.g., token expired (though might be caught earlier)
        // Or weak password errors if Supabase enforces it on update
        return {
            success: false,
            message:
                "Failed to update password. The reset link may have expired or there was a server error.",
        };
    }

    // 3. Password updated successfully, redirect to login
    // Note: We don't necessarily need to sign the user out,
    // Supabase might keep the session active after password update.
    // Redirecting to login provides a clear confirmation.
    redirect("/login?password_updated=true"); // Add query param for potential feedback on login page

    // Technically unreachable due to redirect()
    // return {
    //   success: true,
    //   message: 'Password updated successfully.',
    //   redirect: '/login',
    // };
}
