"use server";

/**
 * Server Action to save debug content to a file. (File saving functionality removed)
 * @param content The content that would have been saved.
 * @param filename Optional name for the file that would have been used.
 */
export async function saveDebugFileAction(
    content: string,
    filename?: string,
): Promise<void> {
    // Only run this action in the development environment
    if (process.env.NODE_ENV !== "development") {
        return;
    }

    try {
        // File saving functionality has been removed.
        // saveToFileUtil(content, filename);
        console.log(
            `[SERVER_ACTION] saveDebugFileAction: Content (length: ${content.length}) for filename '${
                filename ?? "default"
            }' would have been saved (functionality removed).`,
        );
    } catch (error) {
        console.error(
            "[SERVER_ACTION] saveDebugFileAction: Error (though file saving is removed):",
            error,
        );
        // Depending on requirements, you might want to throw the error
        // or return a status object. For now, just logging.
    }
}
