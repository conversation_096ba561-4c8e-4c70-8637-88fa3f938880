import { defineConfig, devices } from "@playwright/test";
import * as dotenv from "dotenv";
import * as path from "path";

// Load environment variables from .env.test
dotenv.config({ path: path.resolve(__dirname, ".env.test") });

export default defineConfig({
    testDir: "./e2e",
    timeout: 30 * 1000,
    expect: {
        timeout: 5000,
    },
    reporter: [
        ["html", { open: "never" }],
        ["list"],
    ],
    webServer: {
        command: "dotenv -e .env.test -c -- npm run dev",
        port: 3000,
        reuseExistingServer: !process.env.CI,
        stdout: "pipe",
        stderr: "pipe",
    },
    use: {
        trace: "on-first-retry",
        screenshot: "only-on-failure",
    },
    projects: [
        {
            name: "setup db",
            testMatch: /global\.setup\.ts/,
            teardown: "cleanup db",
        },
        {
            name: "cleanup db",
            testMatch: /global\.teardown\.ts/,
        },
        {
            name: "setup",
            testMatch: /.*\.setup\.ts/, // Match all setup files including auth.setup.ts and env.setup.ts
        },
        {
            name: "chromium",
            use: {
                ...devices["Desktop Chrome"],
                storageState: "playwright/.auth/user.json",
            },
            dependencies: ["setup"],
        },
        {
            name: "mobile-chrome",
            use: {
                ...devices["Pixel 5"],
                storageState: "playwright/.auth/user.json",
            },
            dependencies: ["setup"],
        },
        {
            name: "accessibility",
            use: {
                ...devices["Desktop Chrome"],
                contextOptions: {
                    reducedMotion: "reduce",
                    forcedColors: "active",
                },
                storageState: "playwright/.auth/user.json",
            },
            dependencies: ["setup"],
        },
    ],
});
