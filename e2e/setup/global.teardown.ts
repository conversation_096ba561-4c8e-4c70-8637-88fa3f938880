import { test as teardown } from "@playwright/test";
import { createClient } from "@supabase/supabase-js";
import type { Database } from "../../src/db/database.types";

teardown("cleanup database", async () => {
    const supabase = createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
    );

    // Delete all data from tables in reverse order of dependencies
    await supabase.from("feedback").delete().neq("id", "");
    await supabase.from("logs").delete().neq("id", "");
    await supabase.from("pages").delete().neq("id", 0);

    console.log("✓ Database cleaned up");
});
