import * as dotenv from "dotenv";
import * as path from "path";
import { test as setup } from "@playwright/test";

// Load environment variables from .env.test
dotenv.config({
    path: path.resolve(process.cwd(), ".env.test"),
    override: true,
});

// Basic verification of environment setup
setup("Verify test environment setup", async () => {
    // Simple validation that required vars exist
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
        throw new Error("NEXT_PUBLIC_SUPABASE_URL is not set in .env.test");
    }

    if (
        !process.env.E2E_TEST_USER_EMAIL || !process.env.E2E_TEST_USER_PASSWORD
    ) {
        throw new Error("Test user credentials are not set in .env.test");
    }
});
