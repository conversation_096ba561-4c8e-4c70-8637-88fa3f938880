import { expect, test } from "@playwright/test";

// Skip these tests until the dashboard page is implemented
test.describe.skip("Authenticated Flow", () => {
    test("should already be logged in", async ({ page }) => {
        // Navigate directly to dashboard
        await page.goto("/dashboard");

        // No login required - should be already authenticated
        // Verify we are on the dashboard without login
        await expect(page.getByRole("heading", { name: "Dashboard" }))
            .toBeVisible();

        // Verify user information is displayed
        await expect(page.getByText("<EMAIL>")).toBeVisible();
    });

    test("should be able to visit protected pages", async ({ page }) => {
        // Navigate to a protected page
        await page.goto("/dashboard/settings");

        // Verify we can access it without login
        await expect(page.getByRole("heading", { name: "Settings" }))
            .toBeVisible();
    });

    test("should be able to log out", async ({ page }) => {
        // Go to dashboard
        await page.goto("/dashboard");

        // Find and click logout button/link
        await page.getByRole("button", { name: "Logout" }).click();

        // Verify we are redirected to login page
        await page.waitForURL("/login");
        await expect(page.getByRole("heading", { name: "Sign in" }))
            .toBeVisible();
    });
});
