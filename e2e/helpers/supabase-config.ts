import { Page } from "@playwright/test";

/**
 * Gets the Supabase configuration being used during tests
 *
 * @param page The Playwright page object
 * @returns Object containing the Supabase URL and a preview of the key
 */
export async function getSupabaseConfig(page: Page) {
    // Navigate to our debug endpoint
    const response = await page.request.get("/api/debug/supabase-config");

    if (!response.ok()) {
        throw new Error(
            `Failed to get Supabase config: ${response.statusText()}`,
        );
    }

    return await response.json();
}

/**
 * Logs Supabase configuration to console during test runs
 *
 * @param page The Playwright page object
 */
export async function logSupabaseConfig(page: Page) {
    try {
        const config = await getSupabaseConfig(page);
        console.log("\n======= SUPABASE TEST CONFIGURATION =======");
        console.log(`URL: ${config.supabaseUrl}`);
        console.log(`Key: ${config.supabaseKeyPreview}`);
        console.log(`Timestamp: ${config.timestamp}`);
        console.log("==========================================\n");
        return config;
    } catch (error) {
        console.error("Error fetching Supabase config:", error);
        return null;
    }
}
