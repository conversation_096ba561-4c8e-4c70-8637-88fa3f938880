# Dokument wymagań produktu (PRD) - Landing Page Now

## 1. <PERSON><PERSON><PERSON><PERSON><PERSON>d produktu

Landing Page Now to aplikacja MVP zap<PERSON>je<PERSON><PERSON>, aby umo<PERSON><PERSON>wić osobom bez wiedzy technicznej tworzenie profesjonalnych stron docelowych (landing pages) w prosty i szybki sposób. Aplikacja wykorzystuje sztuczną inteligencję do generowania gotowego kodu HTML/CSS/JS na podstawie prostych informacji wprowadzonych przez użytkownika.

Główne cechy produktu:

- Intuicyjny interfejs oparty na formularzu z podstawowymi opcjami konfiguracji
- Generowanie stron docelowych przy użyciu modelu AI (Anthropic lub Gemini 2.5)
- Możliwość pobrania gotowego pliku HTML
- System kont użytkowników z ograniczeniem do 3 generacji dziennie
- Wsparcie dla różnych typów stron docelowych (generowanie leadów, sp<PERSON><PERSON><PERSON>, produkt, webinar/wydarzenie)
- Możliwość przesyłania własnych zasobów (obrazy, logo)
- Integracja z popularnymi serwisami do zbierania leadów
- Historia wygenerowanych stron z możliwością podglądu

Aplikacja skupia się na prostocie i wydajności, umożliwiając użytkownikom stworzenie funkcjonalnej strony docelowej w mniej niż 10 minut.

## 2. Problem użytkownika

Tworzenie profesjonalnych stron docelowych tradycyjnie wymaga:

- Znajomości kodowania (HTML/CSS/JS)
- Korzystania ze złożonych platform (WordPress, Wix, itp.)
- Znacznych nakładów czasowych na naukę narzędzi

Istniejące generatory stron często:

- Są nadmiernie skomplikowane dla początkujących
- Wymagają znajomości podstaw projektowania
- Mają strome krzywe uczenia się
- Oferują zbyt wiele opcji, powodując zagubienie użytkownika

Osoby nietechniczne napotykają wysokie bariery wejścia, co uniemożliwia im szybkie tworzenie efektywnych stron docelowych dla swoich produktów lub usług. Landing Page Now rozwiązuje ten problem, oferując prosty interfejs do szybkiego tworzenia stron bez znajomości kodowania, wykorzystując moc sztucznej inteligencji.

## 3. Wymagania funkcjonalne

### 3.1. System uwierzytelniania

- Rejestracja użytkownika z weryfikacją adresu e-mail
- Logowanie do systemu
- Weryfikacja za pomocą Cloudflare CAPTCHA
- Resetowanie hasła

### 3.2. Interfejs użytkownika

- Formularz konfiguracyjny po lewej stronie (zwijany)
- Podgląd strony po prawej stronie
- Opcje konfiguracji:
  - Produkt/Usługa/Temat
  - Grupa docelowa
  - Główne słowa kluczowe
  - Dodatkowe wymagania
  - Opcje stylu projektowego
  - Wybór typu strony docelowej
  - Preferencje językowe

### 3.3. Generowanie stron

- Integracja z jednym modelem AI (Anthropic lub Gemini 2.5)
- Generowanie kodu HTML/CSS/JS w jednym pliku
- Generowanie treści w oparciu o dane wprowadzone przez użytkownika
- Limit 3 generacji dziennie na konto
- Obsługa błędów podczas generowania

### 3.4. Zarządzanie zasobami

- Możliwość przesyłania własnych obrazów
- Możliwość przesyłania własnego logo
- Walidacja przesyłanych plików

### 3.5. Integracje

- Wsparcie dla integracji z serwisami do zbierania leadów poprzez udostępnienie pola formularzu na wklejenie kodu, który dostarcza zewnętrzny serwis.

### 3.6. Eksport i pobieranie

- Pobieranie gotowego pliku HTML
- Brak możliwości edycji kodu po wygenerowaniu

### 3.7. Feedback i analityka

- System zbierania opinii (kciuk w górę/dół z modalem)
- Śledzenie podstawowych metryk:
  - Czas generowania stron
  - Liczba wygenerowanych stron
  - Współczynnik generowania do pobrania

### 3.8. Historia generowanych stron

- Przechowywanie wszystkich wygenerowanych stron w ramach konta użytkownika
- Lista historii z podstawowymi informacjami o stronie (data utworzenia, typ strony, temat)
- Możliwość podglądu każdej wygenerowanej strony
- Sortowanie i filtrowanie historii

## 4. Granice produktu

### 4.1. Zawarte w MVP

- System kont użytkowników z weryfikacją
- Podstawowy interfejs formularza konfiguracji
- Generowanie stron przy użyciu AI
- Podgląd wygenerowanej strony
- Pobieranie gotowego pliku HTML
- Dzienny limit generacji (3 na użytkownika)
- Wsparcie dla różnych typów stron docelowych
- Przesyłanie własnych zasobów (obrazy, logo)
- Wybór języka treści
- Podstawowe integracje z serwisami do zbierania leadów
- Historia wygenerowanych stron z możliwością podglądu

### 4.2. Poza zakresem MVP

- Zaawansowana edycja wygenerowanego kodu
- Hostowanie wygenerowanych stron
- Rozbudowane szablony lub biblioteka komponentów
- Regeneracja strony po zmianach
- Zaawansowane opcje eksportu (ZIP, oddzielne pliki)
- Zaawansowane narzędzia analityczne
- Płatne subskrypcje (tylko system donacji "Buy me a coffee")
- Automatyczne testowanie wygenerowanych stron
- Zaawansowane opcje brandingu

## 5. Historyjki użytkowników

### 5.1. Rejestracja i uwierzytelnianie

#### US-001: Rejestracja nowego użytkownika

Jako nowy użytkownik, chcę się zarejestrować w systemie, aby móc korzystać z funkcji generowania stron.
Kryteria akceptacji:

- Formularz rejestracji zawiera pola: email, hasło, potwierdzenie hasła
- System waliduje poprawność formatu adresu email
- System wymaga silnego hasła (min. 8 znaków, wielkie i małe litery, cyfra, znak specjalny)
- Po rejestracji na adres email wysyłany jest link weryfikacyjny
- Użytkownik nie może generować stron bez weryfikacji adresu email

#### US-002: Logowanie do systemu

Jako zarejestrowany użytkownik, chcę się zalogować do systemu, aby korzystać z mojego konta.
Kryteria akceptacji:

- Formularz logowania zawiera pola: email i hasło
- System waliduje poprawność danych
- Użytkownik ma możliwość zaznaczenia opcji "Zapamiętaj mnie"
- System przekierowuje zalogowanego użytkownika do panelu głównego

#### US-003: Weryfikacja z użyciem CAPTCHA

Jako użytkownik, chcę przejść weryfikację CAPTCHA, aby potwierdzić, że nie jestem botem.
Kryteria akceptacji:

- System wyświetla Cloudflare CAPTCHA podczas rejestracji
- Użytkownik musi pomyślnie przejść weryfikację CAPTCHA, aby kontynuować
- W przypadku niepowodzenia weryfikacji, system wyświetla stosowny komunikat

#### US-004: Resetowanie hasła

Jako użytkownik, chcę zresetować swoje hasło, jeśli je zapomnę.
Kryteria akceptacji:

- System udostępnia funkcję "Zapomniałem hasła"
- Po wprowadzeniu adresu email system wysyła link do resetowania hasła
- Po kliknięciu linku użytkownik może ustawić nowe hasło
- System wyświetla potwierdzenie po pomyślnym zresetowaniu hasła

### 5.2. Konfiguracja i generowanie stron

#### US-005: Konfiguracja podstawowych informacji o stronie

Jako zalogowany użytkownik, chcę wprowadzić podstawowe informacje o mojej stronie, aby AI mogło wygenerować treść dopasowaną do moich potrzeb.
Kryteria akceptacji:

- Formularz zawiera pola: Produkt/Usługa/Temat, Grupa docelowa, Główne słowa kluczowe
- System waliduje, czy wszystkie wymagane pola zostały wypełnione
- System zapisuje wprowadzone dane w sesji użytkownika
- Użytkownik może przejść do następnego kroku konfiguracji

#### US-006: Wybór typu strony docelowej

Jako zalogowany użytkownik, chcę wybrać typ strony docelowej, aby był on dostosowany do moich celów biznesowych.
Kryteria akceptacji:

- System wyświetla dostępne typy stron: Lead Generation, Sales, Product, Webinar/Event
- Użytkownik może wybrać tylko jeden typ strony
- Dla każdego typu strony system wyświetla krótki opis wyjaśniający jego przeznaczenie

#### US-007: Wybór stylu projektowego

Jako zalogowany użytkownik, chcę wybrać styl projektowy mojej strony, aby odpowiadał mojej marce.
Kryteria akceptacji:

- System wyświetla dostępne style projektowe
- Użytkownik może wybrać tylko jeden styl projektowy
- System zapisuje wybrany styl projektowy w sesji użytkownika

#### US-008: Przesyłanie własnych zasobów

Jako zalogowany użytkownik, chcę przesłać własne obrazy i logo, aby dostosować stronę do mojej marki.
Kryteria akceptacji:

- System umożliwia przesyłanie plików obrazów (JPG, PNG, WebP)
- System umożliwia przesyłanie plików logo (JPG, PNG, SVG)
- Maksymalny rozmiar pliku wynosi 2MB
- System waliduje format i rozmiar przesyłanych plików
- Przesłane pliki są zapisywane tymczasowo na serwerze

#### US-009: Wybór języka treści

Jako zalogowany użytkownik, chcę wybrać język treści na mojej stronie, aby dotrzeć do odpowiedniej grupy docelowej.
Kryteria akceptacji:

- System wyświetla listę dostępnych języków
- Użytkownik może wybrać tylko jeden język
- Wybór języka wpływa na język generowanej treści przez AI

#### US-010: Konfiguracja integracji z serwisami do zbierania leadów

Jako zalogowany użytkownik, chcę skonfigurować integrację z serwisem do zbierania leadów, aby zbierać dane kontaktowe od odwiedzających.
Kryteria akceptacji:

- System Pozwala na wprowadzenie kodu HTML/CSS w Javascript który jest dostarczany przez zewnętrzny serwis.
- System zapisuje dane integracji w sesji użytkownika

#### US-011: Generowanie strony

Jako zalogowany użytkownik, chcę wygenerować stronę docelową na podstawie wprowadzonych informacji, aby uzyskać gotowy produkt.
Kryteria akceptacji:

- System waliduje, czy wszystkie wymagane informacje zostały wprowadzone
- System wyświetla informację o pozostałej liczbie generacji w danym dniu
- System inicjuje proces generowania z wykorzystaniem AI
- System wyświetla wskaźnik postępu podczas generowania
- System informuje użytkownika o ewentualnych błędach
- Po zakończeniu generowania system wyświetla podgląd wygenerowanej strony

### 5.3. Podgląd i eksport

#### US-012: Podgląd wygenerowanej strony

Jako zalogowany użytkownik, chcę zobaczyć podgląd wygenerowanej strony, aby ocenić jej wygląd przed pobraniem.
Kryteria akceptacji:

- System wyświetla podgląd wygenerowanej strony w interfejsie aplikacji
- Podgląd jest interaktywny i umożliwia sprawdzenie funkcjonalności strony
- System wyświetla podgląd w różnych szerokościach ekranu (desktop, tablet, mobile)
- Użytkownik nie może edytować kodu strony w podglądzie

#### US-013: Pobranie wygenerowanej strony

Jako zalogowany użytkownik, chcę pobrać wygenerowaną stronę w formacie HTML, aby móc ją opublikować.
Kryteria akceptacji:

- System udostępnia przycisk "Pobierz HTML"
- Po kliknięciu przycisku plik HTML jest pobierany na urządzenie użytkownika
- Plik HTML zawiera wszystkie niezbędne elementy: HTML, CSS, JS w jednym pliku
- Pobrana strona działa poprawnie po otwarciu w przeglądarce
- System rejestruje pobranie strony jako zakończone generowanie

### 5.4. Historia i zarządzanie wygenerowanymi stronami

#### US-020: Przeglądanie historii wygenerowanych stron

Jako zalogowany użytkownik, chcę przeglądać historię wszystkich wygenerowanych przeze mnie stron, aby móc wrócić do wcześniejszych projektów.
Kryteria akceptacji:

- System wyświetla listę wszystkich wygenerowanych stron w sekcji Historia w panelu dashboardu
- Lista zawiera podstawowe informacje o każdej stronie: datę utworzenia, typ strony, temat
- Użytkownik może sortować i filtrować listę według różnych kryteriów
- System umożliwia podgląd każdej strony z historii po kliknięciu na nią
- Historia jest przechowywana w ramach konta użytkownika i dostępna w dowolnym momencie
- System wyświetla informację, jeśli użytkownik nie ma jeszcze żadnych wygenerowanych stron

### 5.5. Feedback i monitorowanie

#### US-014: Przekazanie opinii o wygenerowanej stronie

Jako zalogowany użytkownik, chcę przekazać opinię o wygenerowanej stronie, aby pomóc w ulepszeniu systemu.
Kryteria akceptacji:

- System udostępnia prostą ocenę (kciuk w górę/w dół) dla wygenerowanej strony
- Po wybraniu negatywnej oceny system wyświetla modal z prośbą o dodatkowe informacje
- Użytkownik może wprowadzić dodatkowe uwagi w formie tekstu
- System zapisuje opinię użytkownika w bazie danych
- System dziękuje użytkownikowi za przekazanie opinii

#### US-015: Sprawdzenie liczby pozostałych generacji

Jako zalogowany użytkownik, chcę sprawdzić, ile generacji stron pozostało mi w danym dniu, aby planować moje działania.
Kryteria akceptacji:

- System wyświetla liczbę pozostałych generacji w widocznym miejscu interfejsu
- Licznik jest aktualizowany po każdej generacji
- System wyświetla informację o resetowaniu limitu (np. "Limit resetuje się o północy UTC")
- W przypadku wykorzystania limitu system wyświetla stosowny komunikat Przycisk odpowiadający za rozpoczęcie generowania jest nieaktywny.

### 5.6. Administracja i zarządzanie kontem

#### US-016: Wyświetlenie informacji o koncie

Jako zalogowany użytkownik, chcę zobaczyć informacje o moim koncie, aby zarządzać moimi danymi.
Kryteria akceptacji:

- System wyświetla stronę z informacjami o koncie: email, data rejestracji
- Użytkownik może zmienić hasło
- Użytkownik może zobaczyć statystyki korzystania z systemu (liczba wygenerowanych stron)
- System umożliwia usunięcie konta

#### US-017: Zmiana hasła

Jako zalogowany użytkownik, chcę zmienić swoje hasło, aby zwiększyć bezpieczeństwo mojego konta.
Kryteria akceptacji:

- System wymaga podania aktualnego hasła
- System wymaga podania nowego hasła i jego potwierdzenia
- System waliduje siłę nowego hasła (min. 8 znaków, wielkie i małe litery, cyfra, znak specjalny)
- Po pomyślnej zmianie hasła system wyświetla potwierdzenie
- System wysyła powiadomienie email o zmianie hasła

#### US-018: Usunięcie konta

Jako zalogowany użytkownik, chcę usunąć swoje konto, jeśli nie chcę już korzystać z systemu.
Kryteria akceptacji:

- System wymaga potwierdzenia chęci usunięcia konta
- System wymaga podania hasła dla potwierdzenia
- Po usunięciu konta wszystkie dane użytkownika są usuwane z systemu, Włącznie z historią wygenerowanych stron.
- System wyświetla potwierdzenie usunięcia konta
- Użytkownik jest wylogowywany z systemu

#### US-019: Wsparcie systemu donacji

Jako zadowolony użytkownik, chcę wesprzeć twórców systemu finansowo, aby pomóc w rozwoju projektu.
Kryteria akceptacji:

- System wyświetla opcję "Buy me a coffee" w widocznym miejscu interfejsu
- Po kliknięciu przycisku użytkownik jest przekierowywany do bezpiecznej strony płatności
- System wyświetla podziękowanie po dokonaniu wpłaty
- System nie wymaga donacji do korzystania z funkcji aplikacji

## 6. Metryki sukcesu

### 6.1. Metryki użyteczności

- 80% użytkowników jest w stanie wygenerować działającą stronę w mniej niż 10 minut
- 70% wygenerowanych stron spełnia podstawowe standardy UX/UI
- Współczynnik ukończenia zadania (Task Completion Rate) wynosi co najmniej 90%

### 6.2. Metryki techniczne

- Kod HTML/CSS/JS generowany przez AI działa poprawnie we wszystkich głównych przeglądarkach (Chrome, Firefox, Safari, Edge)
- Wygenerowane strony są w pełni responsywne i dostępne

### 6.3. Metryki biznesowe

- Współczynnik generowania do pobrania (% wygenerowanych stron, które zostały pobrane) wynosi co najmniej 80%

### 6.4. Metryki opinii użytkowników

- Liczba zgłaszanych błędów lub problemów spada o 10% miesięcznie po uruchomieniu
- Co najmniej 50% użytkowników korzysta z więcej niż jednej generacji strony
