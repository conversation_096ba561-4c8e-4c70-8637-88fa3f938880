# OpenRouter Service Implementation Plan

## 1. Service Description

The OpenRouter service provides a TypeScript-based interface for interacting with the OpenRouter API, enabling seamless integration of various LLM models into the application. The service is designed to be type-safe, performant, and maintainable.

## 2. Constructor

```typescript
interface OpenRouterConfig {
  apiKey: string;
  baseUrl?: string;
  defaultModel?: string;
  maxRetries?: number;
  timeout?: number;
}

class OpenRouterService {
  constructor(config: OpenRouterConfig) {
    // Implementation
  }
}
```

## 3. Public Methods and Fields

### Methods

```typescript
interface ChatCompletionRequest {
  messages: Array<{
    role: "system" | "user" | "assistant";
    content: string;
  }>;
  model: string;
  temperature?: number;
  max_tokens?: number;
  response_format?: {
    type: "json_schema";
    json_schema: {
      name: string;
      strict: boolean;
      schema: Record<string, unknown>;
    };
  };
}

class OpenRouterService {
  async createChatCompletion(
    request: ChatCompletionRequest
  ): Promise<ChatCompletionResponse>;
  async validateApiKey(): Promise<boolean>;
  async getAvailableModels(): Promise<Model[]>;
  setConfig(config: Partial<OpenRouterConfig>): void;
}
```

### Fields

```typescript
class OpenRouterService {
  readonly config: OpenRouterConfig;
  readonly isInitialized: boolean;
  readonly lastError: Error | null;
}
```

## 4. Private Methods and Fields

```typescript
class OpenRouterService {
  private client: OpenRouterClient;
  private rateLimiter: RateLimiter;
  private validator: ResponseValidator;

  private async handleRequest<T>(request: Request): Promise<T>;
  private validateResponse<T>(response: unknown, schema: ZodSchema<T>): T;
  private async retryWithBackoff<T>(fn: () => Promise<T>): Promise<T>;
}
```

## 5. Error Handling

### Error Types

```typescript
class OpenRouterError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly statusCode?: number
  ) {
    super(message);
  }
}

class ApiKeyError extends OpenRouterError {}
class RateLimitError extends OpenRouterError {}
class ValidationError extends OpenRouterError {}
class NetworkError extends OpenRouterError {}
```

### Error Handling Strategy

1. Use try-catch blocks for API calls
2. Implement exponential backoff for retries
3. Log errors with appropriate context
4. Return user-friendly error messages
5. Maintain error state for debugging

## 6. Security Considerations

1. API Key Management:

   - Store in environment variables
   - Never expose in client-side code
   - Rotate keys regularly

2. Request Validation:

   - Validate all inputs
   - Sanitize user messages
   - Implement rate limiting

3. Response Handling:
   - Validate all responses
   - Sanitize output
   - Handle sensitive data

## 7. Implementation Steps

1. Setup:

   ```bash
   # Create service directory
   mkdir -p src/services/openrouter
   ```

2. Create core files:

   - `src/services/openrouter/types.ts`
   - `src/services/openrouter/client.ts`
   - `src/services/openrouter/service.ts`
   - `src/services/openrouter/utils.ts`

3. Environment Configuration:

   ```env
   OPENROUTER_API_KEY=your_api_key
   OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
   ```

4. Implementation Order:

   1. Basic service structure
   2. API client implementation
   3. Error handling
   4. Rate limiting
   5. Response validation

## Example Usage

```typescript
const openRouter = new OpenRouterService({
  apiKey: process.env.OPENROUTER_API_KEY!,
  defaultModel: "gpt-3.5-turbo",
});

const response = await openRouter.createChatCompletion({
  messages: [
    {
      role: "system",
      content: "You are a helpful assistant.",
    },
    {
      role: "user",
      content: "Hello!",
    },
  ],
  model: "gpt-3.5-turbo",
  response_format: {
    type: "json_schema",
    json_schema: {
      name: "weather",
      strict: true,
      schema: jsonSchemaObj,
    },
  },
});
```
