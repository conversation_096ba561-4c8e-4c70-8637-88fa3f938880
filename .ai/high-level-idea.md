# Aplikacja - Landing Page Now (MVP)

### Główny problem

Tworzenie profesjonalnych landing page wymaga wiedzy technicznej (HTML/CSS) lub korzystania ze złożonych platform (WordPress, Wix). Osoby nietechniczne napotykają wysokie bariery wejścia, a dostępne generatory są często nadmiernie skomplikowane. Landing Page Now oferuje prosty interfejs do szybkiego tworzenia stron docelowych bez znajomości kodowania.

### Najmniejszy zestaw funkcjonalności

- Formularz z podstawowymi opcjami konfiguracji landing page (styl, układ, treść)
- Interfejs do wprowadzania ogólnego opisu i celu strony
- Przy pomocy AI generujemy treść prompta w tle na podstawie zebranych informacji. Skorzystanie z https://docs.anthropic.com/en/api/prompt-tools-improve
- Integracja z JEDNYM modelem AI (Anthropic lub Gemini 2.5) do generowania kodu HTML/CSS/JS
- Zarówno HTML, CSS i JS będą znajdować się w jednym pliku HTML.
- Podgląd wygenerowanej strony
- Możliwość pobrania gotowego pliku HTML
- System kont użytkowników

### Co NIE wchodzi w zakres MVP

- Zaawansowana edycja wygenerowanego kodu
- Przechowywanie historii wygenerowanych stron
- Hostowanie wygenerowanych stron
- Rozbudowane szablony lub biblioteka komponentów

### Kryteria sukcesu

- 80% użytkowników jest w stanie wygenerować działającą stronę w mniej niż 10 minut
- 70% wygenerowanych stron spełnia podstawowe standardy UX/UI
- Kod HTML/CSS/JS generowany przez AI działa poprawnie w głównych przeglądarkach
