# API Endpoint Implementation Plan: Create Page Configuration

## 1. Przegląd punktu końcowego

Endpoint służący do tworzenia nowej konfiguracji strony. Umożliwia użytkownikowi przesłanie danych konfiguracyjnych, które zostaną zapisane w tabeli `pages` powiązanej z jego kontem. Endpoint integruje walidację danych, autoryzację użytkownika i rejestrację logów błędów.

## 2. Szczegóły żądania

- **Metoda HTTP:** POST
- **Ścieżka URL:** /api/pages (alternatywnie /api/pages/create, zgodnie z konwencją projektu)

### Parametry

- **Wymagane:**

  - **Request Body:** Obiekt JSON zawierający pole `config` zgodne z interfejsem `PageConfig`.

    Przykładowa struktura żądania:

    ```json
    {
      "config": {
        "type": "landing",
        "targetAudience": "small businesses",
        "keywords": ["marketing", "conversion"],
        "style": "modern",
        "language": "pl",
        "integrations": {
          "leadCapture": "formID123"
        }
      }
    }
    ```

- **Opcjonalne:**
  - **integrations:** Pole w obiekcie `config` jest opcjonalne i może być pominięte, jeśli użytkownik nie potrzebuje integracji.

## 3. Wykorzystywane typy

- **CreatePageCommand:** Model komendy przyjmujący obiekt `config` (definiowany w `src/types.ts`).
- **PageConfig:** Struktura danych konfiguracyjnych strony zawierająca pola: `type`, `targetAudience`, `keywords`, `style`, `language` oraz opcjonalne pole `integrations` (z kluczem `leadCapture`).
- **CreatePageResponse:** Model odpowiedzi zawierający:
  - `id` – unikalny identyfikator strony
  - `config` – konfiguracja strony
  - `status` – status operacji (enum o wartościach: PENDING, SUCCESS, FAILED)
  - `created_at` – znacznik czasu utworzenia rekordu

## 4. Szczegóły odpowiedzi

- **Sukces:**
  - Kod statusu: 201 Created
  - Treść odpowiedzi: Obiekt `CreatePageResponse` zawierający szczegóły utworzonej konfiguracji.
- **Błędy:**
  - 400 Bad Request – Nieprawidłowe dane wejściowe (np. błąd walidacji)
  - 401 Unauthorized – Brak autoryzacji użytkownika
  - 500 Internal Server Error – Błąd po stronie serwera lub w bazie danych

## 5. Przepływ danych

1. Klient wysyła żądanie POST do endpointu z danymi konfiguracyjnymi.
2. Endpoint waliduje dane wejściowe przy użyciu narzędzia do walidacji (np. Zod).
3. Warstwa serwisowa (service layer) wykonuje logikę biznesową:
   - Weryfikuje strukturę danych
   - Wstawia nowy rekord do tabeli `pages`
   - W przypadku błędu, loguje szczegóły w tabeli `logs`
4. W przypadku sukcesu, zwracana jest odpowiedź zawierająca utworzony rekord.

## 6. Względy bezpieczeństwa

- **Autoryzacja:** Na tym etapie logika autoryzacji jest pominięta; polityki RLS będą implementowane w przyszłości.
- **Walidacja danych:** Użycie walidacji schematów (np. Zod) dla danych wejściowych, aby zapewnić spójność i poprawność danych.
- **Bezpieczeństwo bazy danych:** Użycie przygotowanych zapytań lub bibliotek ORM/Query Builder, aby zapobiec SQL Injection.

## 7. Obsługa błędów

- **400 Bad Request:** Zwracane, gdy walidacja danych wejściowych zawiedzie.
- **401 Unauthorized:** Zwracane, gdy użytkownik nie jest poprawnie uwierzytelniony.
- **500 Internal Server Error:** Zwracane w przypadku błędu operacji na bazie danych lub nieoczekiwanych wyjątków.
- **Logowanie błędów:** Wszelkie błędy są dokumentowane w tabeli `logs` z informacjami o czasie, czasie trwania operacji oraz komunikatem błędu.

## 8. Rozważania dotyczące wydajności

- **Indeksacja:** Wykorzystanie indeksów na kolumnach takich jak `user_id`, `parent_page_id` oraz `created_at` w tabeli `pages` w celu optymalizacji zapytań.
- **Optymalizacja zapytań:** Staranna optymalizacja zapytań SQL w warstwie serwisowej.
- **Skalowalność:** Możliwość rozszerzenia mechanizmów buforowania i przetwarzania asynchronicznego, jeżeli liczba użytkowników znacząco wzrośnie.

## 9. Etapy wdrożenia

1. **Utworzenie endpointu:** Stworzenie nowego endpointu w Next.js (np. w `/api/pages`).
2. **Konfiguracja autoryzacji:** Dodanie middleware do weryfikacji autoryzacji użytkownika.
3. **Walidacja danych:** Implementacja walidacji danych wejściowych przy użyciu biblioteki typu Zod.
4. **Warstwa serwisowa:** Utworzenie lub rozszerzenie istniejącej warstwy serwisowej odpowiedzialnej za logikę biznesową (np. funkcja `createPage`), która:
   - Wykonuje walidację i operację INSERT do tabeli `pages`.
   - Loguje błędy w tabeli `logs` w przypadku niepowodzenia operacji.
5. **Testowanie:** Opracowanie testów jednostkowych i integracyjnych, aby zapewnić poprawność działania endpointu.
6. **Dokumentacja:** Uaktualnienie dokumentacji API (np. Swagger/OpenAPI) z nowym endpointem oraz szczegółowym opisem parametrów i odpowiedzi.
7. **Code Review i wdrożenie:** Przeprowadzenie code review, wdrożenie na środowisko testowe, monitorowanie oraz optymalizacja w razie potrzeby.
