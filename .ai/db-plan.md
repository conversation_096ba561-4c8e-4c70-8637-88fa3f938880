-- Database Schema for Landing Page Now MVP

-- 1. Enum Types
CREATE TYPE public.generation_status AS ENUM (
'PENDING',
'SUCCESS',
'FAILED'
);

-- 2. Tables Definition

-- Users table (Managed by <PERSON><PERSON><PERSON> Auth, structure shown for reference)
-- CREATE TABLE public.users (
-- id uuid NOT NULL,
-- -- other columns managed by Supabase Auth (email, phone, etc.)
-- CONSTRAINT users_pkey PRIMARY KEY (id)
-- );

-- Pages Table
CREATE TABLE public.pages (
id bigserial NOT NULL,
user_id uuid NOT NULL,
parent_page_id bigint NULL,
config jsonb NOT NULL,
html_content text NOT NULL,
created_at timestamp with time zone NOT NULL DEFAULT now(),
CONSTRAINT pages_pkey PRIMARY KEY (id),
CONSTRAINT pages_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
CONSTRAINT pages_parent_page_id_fkey FOREIGN KEY (parent_page_id) REFERENCES public.pages(id) ON DELETE CASCADE
);
COMMENT ON TABLE public.pages IS 'Stores landing page content and configurations, including versions.';
COMMENT ON COLUMN public.pages.id IS 'Unique identifier for each page version (auto-incrementing).';
COMMENT ON COLUMN public.pages.user_id IS 'Foreign key referencing the user who owns the page.';
COMMENT ON COLUMN public.pages.parent_page_id IS 'Foreign key referencing the parent page version (NULL for the initial version).';
COMMENT ON COLUMN public.pages.config IS 'JSONB storing page configuration options (e.g., target audience, keywords, style, integrations).';
COMMENT ON COLUMN public.pages.html_content IS 'Stores the generated HTML/CSS/JS content for the page.';
COMMENT ON COLUMN public.pages.created_at IS 'Timestamp of when the page version was created.';

-- Logs Table
CREATE TABLE public.logs (
id uuid NOT NULL DEFAULT uuid_generate_v4(),
user_id uuid NOT NULL,
page_id bigint NOT NULL,
generation_duration_ms integer NULL,
status public.generation_status NOT NULL,
error_message text NULL,
created_at timestamp with time zone NOT NULL DEFAULT now(),
CONSTRAINT logs_pkey PRIMARY KEY (id),
CONSTRAINT logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
CONSTRAINT logs_page_id_fkey FOREIGN KEY (page_id) REFERENCES public.pages(id) ON DELETE CASCADE
);
COMMENT ON TABLE public.logs IS 'Stores logs for each page generation attempt.';
COMMENT ON COLUMN public.logs.id IS 'Unique identifier for each log entry.';
COMMENT ON COLUMN public.logs.user_id IS 'Foreign key referencing the user who initiated the generation.';
COMMENT ON COLUMN public.logs.page_id IS 'Foreign key referencing the specific page version being generated.';
COMMENT ON COLUMN public.logs.generation_duration_ms IS 'Duration of the generation process in milliseconds.';
COMMENT ON COLUMN public.logs.status IS 'Status of the generation attempt (PENDING, SUCCESS, FAILED).';
COMMENT ON COLUMN public.logs.error_message IS 'Detailed error message if the generation failed.';
COMMENT ON COLUMN public.logs.created_at IS 'Timestamp of when the log entry was created.';

-- Feedback Table
CREATE TABLE public.feedback (
id uuid NOT NULL DEFAULT uuid_generate_v4(),
user_id uuid NOT NULL,
page_id bigint NOT NULL,
rating boolean NOT NULL,
comment text NULL,
created_at timestamp with time zone NOT NULL DEFAULT now(),
CONSTRAINT feedback_pkey PRIMARY KEY (id),
CONSTRAINT feedback_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
CONSTRAINT feedback_page_id_fkey FOREIGN KEY (page_id) REFERENCES public.pages(id) ON DELETE CASCADE
);
COMMENT ON TABLE public.feedback IS 'Stores user feedback provided for generated pages.';
COMMENT ON COLUMN public.feedback.id IS 'Unique identifier for each feedback entry.';
COMMENT ON COLUMN public.feedback.user_id IS 'Foreign key referencing the user who provided the feedback.';
COMMENT ON COLUMN public.feedback.page_id IS 'Foreign key referencing the specific page version the feedback is for.';
COMMENT ON COLUMN public.feedback.rating IS 'User rating (e.g., true for positive, false for negative).';
COMMENT ON COLUMN public.feedback.comment IS 'Optional textual comment provided by the user.';
COMMENT ON COLUMN public.feedback.created_at IS 'Timestamp of when the feedback was submitted.';

-- 3. Relationships
-- Relationships are defined via FOREIGN KEY constraints within the table definitions.
-- - `pages.user_id` -> `auth.users.id` (Many-to-One)
-- - `pages.parent_page_id` -> `pages.id` (Many-to-One, Hierarchical for versioning)
-- - `logs.user_id` -> `auth.users.id` (Many-to-One)
-- - `logs.page_id` -> `pages.id` (Many-to-One)
-- - `feedback.user_id` -> `auth.users.id` (Many-to-One)
-- - `feedback.page_id` -> `pages.id` (Many-to-One)
-- All foreign keys use `ON DELETE CASCADE` as decided during planning.

-- 4. Indexes
-- Primary key indexes are created automatically.
-- Foreign key indexes are generally recommended for performance. Supabase might create some automatically, but explicit creation ensures they exist.
CREATE INDEX idx_pages_user_id ON public.pages USING btree (user_id);
CREATE INDEX idx_pages_parent_page_id ON public.pages USING btree (parent_page_id);
CREATE INDEX idx_logs_user_id ON public.logs USING btree (user_id);
CREATE INDEX idx_logs_page_id ON public.logs USING btree (page_id);
CREATE INDEX idx_feedback_user_id ON public.feedback USING btree (user_id);
CREATE INDEX idx_feedback_page_id ON public.feedback USING btree (page_id);

-- Indexes on frequently queried timestamp columns
CREATE INDEX idx_pages_created_at ON public.pages USING btree (created_at);
CREATE INDEX idx_logs_created_at ON public.logs USING btree (created_at);
CREATE INDEX idx_feedback_created_at ON public.feedback USING btree (created_at);

-- 5. Row Level Security (RLS) Policies

-- Enable RLS for relevant tables
ALTER TABLE public.pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;

-- Policies for 'pages' table
CREATE POLICY "Allow authenticated users to manage their own pages" ON public.pages
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow service_role full access to pages" ON public.pages
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- Policies for 'logs' table
CREATE POLICY "Allow authenticated users to manage their own logs" ON public.logs
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow service_role full access to logs" ON public.logs
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- Policies for 'feedback' table
CREATE POLICY "Allow authenticated users to manage their own feedback" ON public.feedback
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow service_role full access to feedback" ON public.feedback
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- 6. Additional Considerations
-- - The `auth.users` table is managed by Supabase Authentication. This schema assumes its existence and the presence of a `uuid` primary key named `id`.
-- - `uuid_generate_v4()` function requires the `uuid-ossp` extension, typically enabled in Supabase projects. If not, use `gen_random_uuid()` from `pgcrypto`.
-- - Normalization: The schema appears to be in 3NF. `config` is stored as JSONB for flexibility, which is a form of denormalization acceptable for this use case.
-- - Scalability: The current design is suitable for MVP. Indexing is applied to common query columns. For very high volumes, partitioning `logs` table by `created_at` could be considered later.
