Frontend - Next.js z React dla komponentów interaktywnych:

- Next.js zapewnia pełną funkcjonalność React framework z wbudowanym routingiem, SSR i API routes
- React 19 zapewni interaktywność komponentów
- TypeScript 5 dla statycznego typowania kodu i lepszego wsparcia IDE
- Tailwind 4 pozwala na wygodne stylowanie aplikacji
- Shadcn/ui zapewnia bibliotekę dostępnych komponentów React, na których oprzemy UI

Backend - Supabase jako kompleksowe rozwiązanie backendowe:

- Zapewnia bazę danych PostgreSQL
- Zapewnia SDK w wielu językach, które posłużą jako Backend-as-a-Service
- Jest rozwiązaniem open source, które można hostować lokalnie lub na własnym serwerze
- Posiada wbudowaną autentykację użytkowników

AI - Komunikacja z modelami przez usługę Openrouter.ai:

- Dostęp do szerokiej gamy modeli (OpenAI, Anthropic, Google i wiele innych), które pozwolą nam znaleźć rozwiązanie zapewniające wysoką efektywność i niskie koszta
- Pozwala na ustawianie limitów finansowych na klucze API

CI/CD i Hosting:

- Vercel jako platforma hostingowa:
  - Zero-config deployment zintegrowany z GitHub
  - Zintegrowany pipeline CI/CD
  - Darmowy tier wystarczający dla etapu MVP
  - Automatyczne preview deployments dla pull requestów
  - Natywne wsparcie dla Next.js

Testing:

- Vitest jako framework testów jednostkowych:
  - Integracja z React Testing Library do testowania komponentów
  - MSW do mockowania zapytań fetch
  - Szybkie wykonanie z hot reload
- Playwright do testów end-to-end:
  - Testy w trybie headless w CI
  - Przechwytywanie trace do debugowania
  - Integracja z testami dostępności
