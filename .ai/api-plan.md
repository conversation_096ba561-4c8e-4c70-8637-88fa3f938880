# REST API Plan

## 1. Resources

### 1.1 Users

- Maps to Supabase Auth users table
- Handles authentication and user management
- Contains user profile and settings

### 1.2 Pages

- Maps to `pages` table
- Stores landing page configurations and content
- Supports versioning through parent-child relationships

### 1.3 Logs

- Maps to `logs` table
- Tracks page generation attempts and performance
- Stores error information

### 1.4 Feedback

- Maps to `feedback` table
- Stores user ratings and comments
- Links feedback to specific page versions

## 2. Endpoints

### 2.1 Pages

#### Create Page Configuration

- **Method:** POST
- **Path:** `/pages`
- **Description:** Create new page configuration for generation
- **Request Body:**

```json
{
  "config": {
    "type": "string",
    "targetAudience": "string",
    "keywords": ["string"],
    "style": "string",
    "language": "string",
    "integrations": {
      "leadCapture": "string"
    }
  }
}
```

- **Response:** 201 Created

```json
{
  "id": "number",
  "config": "object",
  "status": "PENDING",
  "created_at": "string"
}
```

- **Errors:**
  - 400: Invalid configuration
  - 403: Daily generation limit exceeded
  - 422: Invalid integration code

#### Upload Asset

- **Method:** POST
- **Path:** `/pages/{pageId}/assets`
- **Description:** Upload image or logo for the page
- **Request Body:** multipart/form-data

```
file: binary
type: "logo" | "image"
```

- **Response:** 201 Created

```json
{
  "url": "string",
  "type": "string"
}
```

- **Errors:**
  - 400: Invalid file
  - 413: File too large
  - 415: Unsupported file type

#### Generate Page

- **Method:** POST
- **Path:** `/pages/{pageId}/generate`
- **Description:** Start page generation process
- **Response:** 202 Accepted

```json
{
  "id": "number",
  "status": "PENDING",
  "estimatedTime": "number"
}
```

- **Errors:**
  - 403: Daily limit exceeded
  - 404: Page not found
  - 409: Generation already in progress

#### Get Page

- **Method:** GET
- **Path:** `/pages/{pageId}`
- **Description:** Get page details and content
- **Query Parameters:**
  - version: string (optional)
- **Response:** 200 OK

```json
{
  "id": "number",
  "config": "object",
  "html_content": "string",
  "created_at": "string",
  "parent_page_id": "number | null",
  "status": "string"
}
```

- **Errors:**
  - 404: Page not found
  - 403: Unauthorized access

#### List Pages

- **Method:** GET
- **Path:** `/pages`
- **Description:** List user's pages
- **Query Parameters:**
  - page: number
  - limit: number
  - status: string
- **Response:** 200 OK

```json
{
  "items": [
    {
      "id": "number",
      "config": "object",
      "created_at": "string",
      "status": "string"
    }
  ],
  "total": "number",
  "page": "number",
  "limit": "number"
}
```

#### Download Page

- **Method:** GET
- **Path:** `/pages/{pageId}/download`
- **Description:** Download generated HTML file
- **Response:** 200 OK
  Content-Type: text/html
- **Errors:**
  - 404: Page not found
  - 403: Unauthorized access

### 2.3 Feedback

#### Submit Feedback

- **Method:** POST
- **Path:** `/pages/{pageId}/feedback`
- **Description:** Submit user feedback for a page
- **Request Body:**

```json
{
  "rating": "boolean",
  "comment": "string"
}
```

- **Response:** 201 Created

```json
{
  "id": "uuid",
  "created_at": "string"
}
```

- **Errors:**
  - 404: Page not found
  - 409: Feedback already submitted

#### Get Generation Stats

- **Method:** GET
- **Path:** `/users/me/stats`
- **Description:** Get user's generation statistics
- **Response:** 200 OK

```json
{
  "generationsToday": "number",
  "generationsRemaining": "number",
  "resetTime": "string"
}
```

## 3. Authentication and Authorization

### 3.1 Authentication

- Uses Supabase Auth for user management
- JWT-based authentication
- Tokens provided in Authorization header
- CAPTCHA required for registration and login
- Email verification required for full access

### 3.2 Authorization

- Row Level Security (RLS) policies enforced at database level
- Resource ownership verified for all operations
- Service role access restricted to internal operations
- Generation limits enforced per user

## 4. Validation and Business Logic

### 4.1 User Management

- Email format validation
- Password strength requirements:
  - Minimum 8 characters
  - At least one uppercase letter
  - At least one lowercase letter
  - At least one number
  - At least one special character
- CAPTCHA verification for registration and login
- Email verification required

### 4.2 Page Generation

- Daily limit of 3 generations per user
- Asset upload restrictions:
  - Maximum file size: 2MB
  - Allowed formats: JPG, PNG, WebP, SVG (for logos)
- Configuration validation:
  - Required fields: type, targetAudience, keywords
  - Valid page types: Lead Generation, Sales, Product, Webinar/Event
  - Valid language codes
  - Integration code validation

### 4.3 Feedback

- One feedback submission per user per page
- Rating required, comment optional
- Automatic analytics tracking

### 4.4 Performance Requirements

- Generation process timeout: 10 minutes
- API rate limiting:
  - Authentication endpoints: 10 requests per minute
  - Generation endpoints: 3 requests per day
  - Other endpoints: 60 requests per minute
- Response time targets:
  - Authentication operations: < 500ms
  - Page listing: < 1000ms
  - Generation initiation: < 2000ms
