# Architektura UI dla Landing Page Now

## 1. Przegl<PERSON><PERSON> struktury UI

Architektura interfejsu opiera się na wspólnym layoucie Next.js App Router z dwoma głównymi regionami: sta<PERSON><PERSON> Sidebar, zawierającym nawigację po zakładkach konta i opcję wsparcia "Buy me a coffee", oraz głównym obszarem treści chronionym route guardem, w którym renderują się poszczególne widoki (Dashboard, Generowanie, Profil, Statystyki, <PERSON><PERSON><PERSON>, Usunięcie konta). Layout otacza również Header z avataru użytkownika, licznikiem pozostałych generacji oraz bannerem przy wyczerpanym limicie. Globalnie osadzony jest ToastProvider do komunikacji błędów sieciowych i limitów oraz ModalProvider dla feedbacku.

Responsywność realizowana jest przez Tailwind 4 breakpoints (sm:, md:, lg:). Dostępność zapewnia biblioteka Shadcn/ui (ARIA, focus-visible i kontrast zgodny z WCAG 2.1). Autoryzacja oparta na httpOnly cookie z JWT, a wszystkie widoki poza `/login` i `/register` chronione są przekierowaniem niezalogowanych.

## 2. Lista widoków

### Widok Marketingowy (Landing)

Ścieżka: `/`  
Cel: prezentacja marketingowej strony startowej, opis produktu i CTA do rejestracji lub zakupu planu.  
Kluczowe informacje: hero z nagłówkiem i podtytułem, lista kluczowych funkcji, referencje/testymoniale, przyciski CTA (`Zarejestruj się`, `Dowiedz się więcej`), stopka z podstawowymi linkami to privacy policy, terms of use, contact us.  
Komponenty: `HeroSection`, `FeaturesList`, `Testimonials`, `CTAButtons`, `Footer`  
UX/A11Y/Bezpieczeństwo: text kontrast, ARIA roles, alt-teksty obrazów, responsywność, nie wymaga autoryzacji.

### Widok Logowania i Rejestracji

Ścieżki: `/login`, `/register`  
Cel: umożliwienie nowym użytkownikom rejestracji oraz zalogowania się w systemie.  
Kluczowe informacje: pola email, hasło, potwierdzenie hasła (tylko rejestracja), CAPTCHA Cloudflare, link do resetu hasła.  
Komponenty: `FormField`, `Button`, `ErrorInline`, `Toast`  
UX/A11Y/Bezpieczeństwo: walidacja inline przy użyciu Server Actions, ARIA-labels, zabezpieczenie CAPTCHA, obsługa błędów serwera jako toast.

### Dashboard

Ścieżka: `/dashboard`  
Cel: lista istniejących stron użytkownika z paginacją.  
Kluczowe informacje: tabela kart ze `id`, `config`, `created_at`, `status`.  
Komponenty: `PageList`, `Pagination`, `Button (Nowa strona)`, `Toast`  
UX/A11Y/Bezpieczeństwo: czytelne nagłówki tabeli, focus-visible na elementach paginacji, inline error przy problemie z GET `/pages`.

### Widok Generowania strony

Ścieżka: `/pages/[pageId]`  
Cel: przeprowadzenie konfiguracji landing page oraz uruchomienie generowania.  
Kluczowe informacje: akordeony z etapami konfiguracji (Produkt/Usługa, Grupa docelowa, Słowa kluczowe, Dodatkowe wymagania, Styl, Typ strony, Język, Integracje), stan licznika generacji, baner limitu, przycisk "Generate", pasek postępu oraz sandboxowane `<iframe>` z podglądem.  
Komponenty: `AccordionKonfiguracji`, `ProgressBar`, `IFramePreview`, `Button`, `BannerLimit`, `Toast`, `ModalFeedback`  
UX/A11Y/Bezpieczeństwo: akordeony dostępne klawiaturowo (aria-expanded), bloki akcji z czytelnymi etykietami, focus-visible, obsługa błędów walidacji inline, błędy sieciowe jako toast, `<iframe sandbox>` dla integracji.

### Profil

Ścieżka: `/profile`  
Cel: wyświetlenie informacji o koncie i statystyk podstawowych.  
Kluczowe informacje: email, data rejestracji, liczba wygenerowanych stron.  
Komponenty: `ProfileCard`, `Button`  
UX/A11Y/Bezpieczeństwo: ochrona route guardem, ARIA roles, inline error przy pobieraniu.

### Statystyki

Ścieżka: `/stats`  
Cel: prezentacja limitu i wykorzystania generacji (`generationsToday`, `generationsRemaining`, `resetTime`).  
Kluczowe informacje: licznik generacji, wykres/pasek zużycia.  
Komponenty: `StatsChart`, `StatsCard`, `Toast`  
UX/A11Y/Bezpieczeństwo: czytelne opisy, aria-live dla aktualizacji stanu.

### Zmiana hasła

Ścieżka: `/change-password`  
Cel: umożliwienie bezpiecznej zmiany hasła.  
Kluczowe informacje: pola aktualne hasło, nowe hasło, potwierdzenie nowego, wymagania siły hasła.  
Komponenty: `FormField`, `Button`, `ErrorInline`, `Toast`  
UX/A11Y/Bezpieczeństwo: walidacja inline, ocena siły hasła, ochrona Server Action, obsługa błędów jako toast.

### Usunięcie konta

Ścieżka: `/delete-account`  
Cel: proces trwałego usunięcia konta.  
Kluczowe informacje: potwierdzenie hasła, komunikat ostrzegawczy.  
Komponenty: `ModalConfirm`, `FormField`, `Button`, `Toast`  
UX/A11Y/Bezpieczeństwo: modal z focus trap, ARIA-modal, ochrona Server Action.

## 3. Mapa podróży użytkownika

Użytkownik niezalogowany trafia na `/login` lub `/register`. Po pomyślnym zalogowaniu następuje redirect do `/dashboard`. Tam może przejrzeć listę stron lub rozpocząć nową konfigurację, co tworzy zasób POST `/pages` i przekierowuje do `/pages/[pageId]`. W widoku generowania wypełnia akordeony, klika "Generate" (POST `/pages/{pageId}/generate`), widzi pasek postępu, a po zakończeniu `<iframe>` z podglądem. Następnie opcjonalny feedback przez modal (POST `/pages/{pageId}/feedback`) i pobranie HTML (GET `/pages/{pageId}/download`). Z sidebaru użytkownik przechodzi do Profilu, Statystyk, Zmiany hasła lub Usunięcia konta.

## 4. Układ i struktura nawigacji

Stały Sidebar po lewej z linkami do: Home (`/`), Dashboard, Profil, Statystyki, Zmiana hasła, Usuń konto, "Buy me a coffee". Header nad MainContent z avatar, licznikiem generacji oraz bannerem limitu. ProtectedLayout chroni wszystkie trasy oprócz `/`, `/login` i `/register`. Routing oparty na Next.js App Router.

## 5. Kluczowe komponenty

**AccordionKonfiguracji** – pojedynczy komponent zarządzający wszystkimi sekcjami konfiguracji.  
**ProgressBar** – wizualizacja statusu generowania.  
**IFramePreview** – bezpieczny sandbox do podglądu.  
**PageList & Pagination** – lista stron z paginacją.  
**ModalFeedback** – ocena kciukiem i komentarz.  
**ToastProvider** – globalna obsługa toastów.  
**ProtectedRoute/ProtectedLayout** – route guard.  
**Sidebar & Header** – podstawowa nawigacja i stan konta.  
**FormField, Button, ErrorInline** – budulec wszystkich formularzy.

_Wszystkie komponenty korzystają z Shadcn/ui i Tailwind 4, dbając o ARIA, focus-visible i kontrast zgodny z WCAG 2.1._
