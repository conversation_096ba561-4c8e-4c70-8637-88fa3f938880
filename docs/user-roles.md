# User Roles System

This document describes the user roles system implemented in Landing Page Now.

## Overview

The application implements a simple two-tier role system:

- **Admin**: Full access to system administration and real cost data
- **Subscriber**: Standard user with access to page generation and platform pricing

## Role Storage

User roles are stored in Supabase Auth user metadata using the `role` field:

```json
{
  "user_metadata": {
    "role": "admin" | "subscriber"
  }
}
```

Default role for new users is `subscriber`.

## Setting User Roles

### Method 1: Manual Assignment via Dashboard

1. Access Supabase Dashboard
2. Navigate to Authentication > Users
3. Select a user
4. Look for "Raw JSON" tab or scroll down to find User Metadata section
5. Edit the user metadata
6. Add or modify the `role` field with value `admin` or `subscriber`

### Method 2: SQL Editor (Recommended)

If you can't find the metadata editing option in the dashboard, use the SQL Editor:

1. **Navigate to SQL Editor** in Supabase Dashboard
2. **Execute one of the following SQL commands:**

#### Set admin role for a specific user by email:

```sql
UPDATE auth.users
SET raw_user_meta_data = jsonb_set(
  COALESCE(raw_user_meta_data, '{}'::jsonb),
  '{role}',
  '"admin"'
)
WHERE email = '<EMAIL>';
```

#### Set admin role and preserve existing metadata:

```sql
UPDATE auth.users
 SET raw_user_meta_data = jsonb_set(
  COALESCE(raw_user_meta_data, '{}'::jsonb),
   '{role}',
   '"admin"'
 )
 WHERE email = '<EMAIL>';
```

#### Replace entire metadata (if you want to start fresh):

```sql
UPDATE auth.users
SET raw_user_meta_data = '{"role": "admin"}'::jsonb
WHERE email = '<EMAIL>';
```

#### Set subscriber role (or remove admin privileges):

```sql
UPDATE auth.users
 SET raw_user_meta_data = jsonb_set(
  COALESCE(raw_user_meta_data, '{}'::jsonb),
   '{role}',
   '"subscriber"'
 )
 WHERE email = '<EMAIL>';
```

#### Remove role completely (user will default to subscriber):

```sql
UPDATE auth.users
SET raw_user_meta_data = raw_user_meta_data - 'role'
WHERE email = '<EMAIL>';
```

3. **Verify the change:**

```sql
SELECT email, raw_user_meta_data
FROM auth.users
WHERE email = '<EMAIL>';
```

### Method 3: Bulk Role Assignment

To set multiple users as admins:

```sql
UPDATE auth.users
SET raw_user_meta_data = jsonb_set(
  COALESCE(raw_user_meta_data, '{}'::jsonb),
  '{role}',
  '"admin"'
)
WHERE email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
);
```

### Example User Metadata

After setting the admin role, the `raw_user_meta_data` should look like:

```json
{
  "email_verified": true,
  "role": "admin"
}
```

### Important Notes

- **Replace `'<EMAIL>'`** with the actual email address
- **User must log out and log in again** for changes to take effect
- **Changes are immediate** - no restart required
- **Always verify** the change with the SELECT query
- **Use `COALESCE`** to handle users with null metadata

## Role-Based Features

### Admin Features

- **Real Cost Visibility**: See actual API costs and margins
- **Admin Dashboard**: Access to `/admin` route with system statistics
- **Financial Analytics**: Platform revenue, margins, and cost breakdowns
- **Debug Information**: Detailed usage logs and cost tracking
- **System Statistics**: User counts, generation metrics, and health monitoring

### Subscriber Features

- **Platform Pricing**: See standardized pricing ($0.50 per page)
- **Usage Tracking**: Number of pages generated and total value
- **Standard Dashboard**: Access to page generation and history
- **Limited Analytics**: Basic usage information without cost details

## Implementation Details

### Server-Side Role Checking

```typescript
import {
  getCurrentUserWithRole,
  requireCurrentUserAdmin,
} from "@/lib/auth/roles";

// Get current user with role
const user = await getCurrentUserWithRole();

// Require admin access (throws error if not admin)
await requireCurrentUserAdmin();
```

### Client-Side Role Checking

```typescript
import { useUserRole } from "@/hooks/useUserRole";
import { AdminOnly, RoleGuard } from "@/components/auth/RoleGuard";

// Hook usage
const { role, isAdmin, isSubscriber, isLoading } = useUserRole();

// Component usage
<AdminOnly fallback={<SubscriberView />}>
  <AdminView />
</AdminOnly>;
```

### Route Protection

The middleware automatically protects `/admin/*` routes:

```typescript
// middleware.ts
if (request.nextUrl.pathname.startsWith("/admin")) {
  // Check user role and redirect if not admin
}
```

## Cost Display Logic

### For Subscribers

- Show platform pricing: $0.50 per page
- Hide real API costs
- Display total value based on platform pricing
- Show encouraging messages about premium AI service

### For Admins

- Show real API costs with full precision
- Display platform revenue and margins
- Show cost breakdowns by model and attempt
- Include financial analytics and profit metrics

## API Endpoints

### Admin-Only Endpoints

- `GET /api/admin/stats` - System statistics and financial data

### Role-Aware Endpoints

- `PATCH /api/logs` - Returns cost data (filtered by role)
- `GET /api/pages` - Page listing (cost display varies by role)

## Security Considerations

1. **Server-Side Validation**: All role checks are performed server-side
2. **Metadata Security**: User metadata is only modifiable by admins
3. **API Protection**: Admin endpoints require role verification
4. **Client-Side Hiding**: UI elements are hidden but data is filtered server-side

## Future Enhancements

1. **Role Management UI**: Admin interface for managing user roles
2. **Additional Roles**: Potential for more granular permissions
3. **Audit Logging**: Track role changes and admin actions
4. **Bulk Operations**: Mass role assignment tools

## Testing

To test the role system:

1. Create a test user account
2. Set role to `admin` via Supabase Dashboard
3. Verify admin features are accessible
4. Change role to `subscriber` and verify restrictions
5. Test middleware protection on `/admin` routes

## Troubleshooting

### Common Issues

1. **Role not updating**: Clear browser cache and re-login
2. **Admin routes inaccessible**: Verify role is set correctly in user metadata
3. **Cost data not showing**: Check if user has admin role and API permissions

### Debugging

```typescript
// Check current user role
const { role } = useUserRole();
console.log("Current user role:", role);

// Server-side debugging
const user = await getCurrentUserWithRole();
console.log("User role:", user?.role);
```
