# Cost Tracking

This document outlines how we track costs for AI-generated landing pages in the application.

## Overview

Our application uses OpenRouter API for AI content generation, primarily with Claude 3.7 Sonnet model. To provide users with accurate cost information, we track costs directly from the OpenRouter API using their dedicated cost tracking endpoint.

## Cost Tracking Architecture

Our cost tracking follows these steps:

1. During each generation attempt, we collect the generation ID returned in the API response
2. After all generation attempts are complete, we fetch detailed cost information for each generation ID using OpenRouter's `/generation` endpoint
3. We aggregate these costs to get the total cost across all attempts

This approach ensures we're using the actual billable cost data directly from OpenRouter, rather than estimates or calculations.

## OpenRouter Generation Details API

We use the [GET /generation](https://openrouter.ai/docs/api-reference/get-a-generation) endpoint, which returns detailed information about a specific generation, including:

```json
{
  "data": {
    "id": "gen-**********-ou3hnSxynxlaFS84XPB2",
    "total_cost": 0.006444,
    "created_at": "2025-05-18T09:13:30.852659+00:00",
    "model": "anthropic/claude-3-7-sonnet-20250219",
    "tokens_prompt": 26,
    "tokens_completion": 383,
    "usage": 0.006444,
    "provider_name": "Google"
  }
}
```

### Handling Delayed Generation Data

Sometimes the generation data isn't immediately available in OpenRouter's system. To handle this:

1. If we get a 404 error when requesting generation details, we wait 3 seconds and retry
2. We make up to 3 retry attempts before considering the request failed
3. If we can't fetch details for any generation after all retries, we fall back to a token-based estimate:
   - Input tokens: $0.003 per 1K tokens
   - Output tokens: $0.015 per 1K tokens

## Cost Tracking Components

### Per-Attempt Tracking

For each generation attempt, we:

- Store the generation ID
- Track token usage (prompt tokens, completion tokens, total tokens)

### Cost Aggregation

After all generation attempts, we:

- Fetch cost details for each generation ID
- Sum the total_cost values to get the cumulative cost
- Store this aggregated cost in our database

## Database Schema

Costs are stored in the `logs` table with these fields:

- `prompt_tokens`: Number of prompt tokens
- `completion_tokens`: Number of completion tokens
- `total_tokens`: Total tokens used
- `prompt_cost`: Cost of prompt tokens in USD (if available)
- `completion_cost`: Cost of completion tokens in USD (if available)
- `total_cost`: Total cost in USD from the API
- `cost_currency`: Currency (always USD)
- `model`: Model used for generation
- `generation_attempts`: Number of attempts made
- `cumulative_tokens`: Total tokens across all attempts
- `cumulative_cost`: Total cost across all attempts

## Implementation Details

The cost tracking implementation is in:

- `src/services/openrouter/service.ts`: Direct API interaction with OpenRouter
- `src/services/page-generator/service.ts`: Tracking costs across multiple generation attempts
- `supabase/migrations/20250613000000_add_cost_to_logs.sql`: Database schema

## Cost Display

Users can see cost information in:

- Generation history
- Usage statistics dashboard
- Cost savings metrics (comparing actual vs. maximum possible costs)
