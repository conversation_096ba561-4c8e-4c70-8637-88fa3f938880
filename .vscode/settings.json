{"deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "sonarlint.connectedMode.project": {"connectionId": "SonarQube", "projectKey": "marcink<PERSON><PERSON><PERSON><PERSON>_landingpagenow"}}