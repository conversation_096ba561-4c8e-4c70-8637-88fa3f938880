# Landing Page Now

AI-powered landing page generator for non-technical people.

## Project Description

Landing Page Now is an MVP application designed to help non-technical people create professional landing pages quickly and easily. The application leverages artificial intelligence to generate ready-to-use HTML/CSS/JS code based on simple information provided by the user.

**Key Features:**

- Intuitive form-based interface with basic configuration options
- AI-powered landing page generation (using Anthropic or Gemini 2.5)
- Download of ready HTML files
- User account system with daily generation limits
- Support for different landing page types (lead generation, sales, product, webinar/event)
- Custom asset upload capability (images, logo)
- Integration with popular lead collection services

The application focuses on simplicity and efficiency, allowing users to create a functional landing page in less than 10 minutes.

## Tech Stack

### Frontend

- [Next.js 15](https://nextjs.org/) - React framework with built-in routing, SSR, and API routes
- [React 19](https://react.dev/) - For interactive components
- [TypeScript 5](https://www.typescriptlang.org/) - Static typing for better code quality
- [Tailwind 4](https://tailwindcss.com/) - Utility-first CSS framework
- [Shadcn/ui](https://ui.shadcn.com/) - Accessible React component library

### Backend

- [Supabase](https://supabase.io/) - Comprehensive backend solution including:
  - PostgreSQL database
  - Multi-language SDK as Backend-as-a-Service
  - Built-in user authentication

### AI Integration

- [Openrouter.ai](https://openrouter.ai/) - Provides access to various AI models (OpenAI, Anthropic, Google)

### CI/CD & Hosting

- [Vercel](https://vercel.com/) - Platform for deployment with:
  - Zero-config deployment integrated with GitHub
  - Integrated CI/CD pipeline
  - Native Next.js support

### Testing

- [Vitest](https://vitest.dev/) - Unit testing framework with:
  - React Testing Library integration for component testing
  - MSW for fetch mocking
- [Playwright](https://playwright.dev/) - End-to-end testing framework with:
  - Headless browser testing in CI
  - Trace capture for debugging

See [TESTING.md](./TESTING.md) for detailed information about testing strategy and practices.

## Getting Started Locally

### Prerequisites

- Node.js 22.14.0 (we recommend using [nvm](https://github.com/nvm-sh/nvm) to manage Node.js versions)

### Installation

1. Clone the repository

```bash
git clone https://github.com/your-username/landingpagenow.git
cd landingpagenow
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables (create a `.env.local` file based on `.env.example` if available)

4. Start the development server

```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Available Scripts

- `npm run dev` - Starts the development server with Turbopack
- `npm run build` - Builds the application for production
- `npm run start` - Starts the production server
- `npm run lint` - Runs ESLint to check code quality

## Project Scope

### MVP Features

- User account system with email verification
- Basic configuration form interface
- AI-powered page generation
- Generated page preview
- HTML file download
- Daily generation limit (3 per user)
- Support for different landing page types
- Custom asset upload (images, logo)
- Content language selection
- Basic integrations with lead collection services
- Simple feedback system (thumbs up/down with feedback modal)

### Out of Scope for MVP

- Advanced editing of generated code
- Generated page history storage
- Hosting of generated pages
- Advanced templates or component libraries
- Page regeneration after changes
- Advanced export options (ZIP, separate files)
- Advanced analytics tools
- Paid subscriptions (only "Buy me a coffee" donation system)
- Automated testing of generated pages
- Advanced branding options

## Project Status

The project is currently in MVP development stage.

### Success Metrics

- 80% of users can generate a functional page in less than 10 minutes
- 70% of generated pages meet basic UX/UI standards
- Task Completion Rate of at least 90%
- Generated code works correctly in all major browsers
- Generated pages are fully responsive and accessible
- Generation-to-download rate of at least 80%

## License

This project is proprietary and private. All rights reserved.

---

_Note: This README will be updated as the project evolves._
