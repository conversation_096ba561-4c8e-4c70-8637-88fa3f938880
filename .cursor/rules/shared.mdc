---
description: 
globs: 
alwaysApply: true
---
# AI Rules for Landing Page Now

Landing Page Now is a web application that allows users to generate landing pages utilizing LLMs. The main aim of the project is to help non-technical people to build high converting landing pages.

## Tech Stack

- Next.js 15
- TypeScript 5
- React 19
- Tailwind 4
- Shadcn/ui

## Project structure 

- `./src` - source code
- `./src/app` - Next.js app router
- `./midleware.ts` - Next.js middleware
- `./src/db` - Supabase clients and types
- `./src/components` - Client and server side components written in React

When modifying the directory structure, always update this section.

## Coding practices 

### Guidelines for clean code 

- Use feedback from linters to improve the code when making changes. 
- Prioritize error handling and edge cases. 
- Handle errors and edge cases at the beginning of functions. 
- Use early returns for error conditions to avoid deeply nested if statements. 
- Place the happy path last in the function for improved readability. 
- Avoid unnecessary else statements; use if-return pattern instead. 
- Use guard clauses to handle preconditions and invalid states early. 
- Implement proper error logging and user-friendly error messages. 
- Consider using custom error types or error factories for consistent error handling.

### Next.js 

- Use App Router and Server Components for improved performance and SEO
- Implement route handlers for API endpoints instead of the pages/api directory
- Use server actions for form handling and data mutations from Server Components
- Leverage Next.js Image component with proper sizing for core web vitals optimization
- Implement the Metadata API for dynamic SEO optimization
- Use React Server Components for {{data_fetching_operations}} to reduce client-side JavaScript
- Implement Streaming and Suspense for improved loading states
- Use the new Link component without requiring a child <a> tag
- Leverage parallel routes for complex layouts and parallel data fetching
- Implement intercepting routes for modal patterns and nested UIs
- Make sure to add 'use client' for pages / components where the hooks like `useEffect` are used

### Supabase 
- Always use supabase.auth.getUser() to protect pages and user data.
- Never trust supabase.auth.getSession() inside server code such as middleware.

