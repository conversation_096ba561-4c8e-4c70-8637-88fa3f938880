---
description: 
globs: *.tsx
alwaysApply: false
---
- Use functional components with hooks instead of class components
- Implement React.memo() for expensive components that render often with the same props
- Utilize React.lazy() and Suspense for code-splitting and performance optimization
- Use the useCallback hook for event handlers passed to child components to prevent unnecessary re-renders
- Prefer useMemo for expensive calculations to avoid recomputation on every render
- Implement useId() for generating unique IDs for accessibility attributes
- Use the new use hook for data fetching in React 19+ projects
- Leverage Server Components for {{data_fetching_heavy_components}} when using React with Next.js or similar frameworks
- Consider using the new useOptimistic hook for optimistic UI updates in forms
- Use useTransition for non-urgent state updates to keep the UI responsive
- Make sure functions follow the MAX cognitive complexity rule of 15. Break down complex functions into smaller pieces.
- Use ?? instead of ||
- Prefer using an optional chain expression instead, as it's more concise and easier to read.
- Extract nested ternary operations into an independent statements.

