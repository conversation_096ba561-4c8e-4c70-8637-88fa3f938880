name: Pull Request Workflow

on:
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened]

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

  unit-test:
    name: Unit Tests
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests with coverage
        run: npm run test:coverage

      - name: Upload test coverage
        uses: actions/upload-artifact@v4
        if: success()
        with:
          name: unit-test-coverage
          path: coverage/
          retention-days: 7

  e2e-test:
    name: E2E Tests
    needs: lint
    runs-on: ubuntu-latest
    environment: integration
    env:
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      SUPABASE_DEFAULT_USER_ID: ${{ secrets.SUPABASE_DEFAULT_USER_ID }}
      NEXT_PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY: ${{ secrets.NEXT_PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY }}
      CLOUDFLARE_TURNSTILE_SECRET_KEY: ${{ secrets.CLOUDFLARE_TURNSTILE_SECRET_KEY }}
      OPEN_ROUTER_API_KEY: ${{ secrets.OPEN_ROUTER_API_KEY }}
      OPENROUTER_BASE_URL: ${{ secrets.OPENROUTER_BASE_URL }}
      OPENROUTER_DEFAULT_MODEL: ${{ secrets.OPENROUTER_DEFAULT_MODEL }}
      E2E_TEST_USER_EMAIL: ${{ secrets.E2E_TEST_USER_EMAIL }}
      E2E_TEST_USER_PASSWORD: ${{ secrets.E2E_TEST_USER_PASSWORD }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install chromium

      - name: Confirm environment
        run: echo "Using integration environment"

      - name: Create .env.test file
        run: |
          echo "NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL" > .env.test
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY" >> .env.test
          echo "SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY" >> .env.test
          echo "SUPABASE_DEFAULT_USER_ID=$SUPABASE_DEFAULT_USER_ID" >> .env.test
          echo "NEXT_PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY=$NEXT_PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY" >> .env.test
          echo "CLOUDFLARE_TURNSTILE_SECRET_KEY=$CLOUDFLARE_TURNSTILE_SECRET_KEY" >> .env.test
          echo "OPEN_ROUTER_API_KEY=$OPEN_ROUTER_API_KEY" >> .env.test
          echo "OPENROUTER_BASE_URL=$OPENROUTER_BASE_URL" >> .env.test
          echo "OPENROUTER_DEFAULT_MODEL=$OPENROUTER_DEFAULT_MODEL" >> .env.test
          echo "E2E_TEST_USER_EMAIL=$E2E_TEST_USER_EMAIL" >> .env.test
          echo "E2E_TEST_USER_PASSWORD=$E2E_TEST_USER_PASSWORD" >> .env.test

      - name: Run E2E tests
        run: npm run e2e

      - name: Upload Playwright test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: test-results/
          retention-days: 7

      - name: Upload Playwright traces
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-traces
          path: playwright-report/
          retention-days: 7

  status-comment:
    name: Status Comment
    if: ${{ success() }}
    needs: [lint, unit-test, e2e-test]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create status comment
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const lintStatus = '${{ needs.lint.result }}' === 'success' ? '✅' : '❌';
            const unitTestStatus = '${{ needs.unit-test.result }}' === 'success' ? '✅' : '❌';
            const e2eTestStatus = '${{ needs.e2e-test.result }}' === 'success' ? '✅' : '❌';

            const overallStatus = 
              lintStatus === '✅' && 
              unitTestStatus === '✅' && 
              e2eTestStatus === '✅' ? '✅ All checks passed!' : '❌ Some checks failed!';

            const comment = `## Pull Request Status

            | Check | Status |
            | ----- | ------ |
            | Lint | ${lintStatus} |
            | Unit Tests | ${unitTestStatus} |
            | E2E Tests | ${e2eTestStatus} |

            **Overall Status:** ${overallStatus}

            _Generated by GitHub Actions at ${new Date().toISOString()}_`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
