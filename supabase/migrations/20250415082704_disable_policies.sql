-- Migration: Disable all policies from initial schema
-- Description: Disables all Row Level Security (RLS) policies created in the initial schema
-- Created at: 2025-04-15 08:27:04 UTC

-- Disable policies for 'pages' table
drop policy if exists "authenticated_select_pages" on public.pages;
drop policy if exists "authenticated_insert_pages" on public.pages;
drop policy if exists "authenticated_update_pages" on public.pages;
drop policy if exists "authenticated_delete_pages" on public.pages;
drop policy if exists "service_role_all_pages" on public.pages;

-- Disable policies for 'logs' table
drop policy if exists "authenticated_select_logs" on public.logs;
drop policy if exists "authenticated_insert_logs" on public.logs;
drop policy if exists "authenticated_update_logs" on public.logs;
drop policy if exists "authenticated_delete_logs" on public.logs;
drop policy if exists "service_role_all_logs" on public.logs;

-- Disable policies for 'feedback' table
drop policy if exists "authenticated_select_feedback" on public.feedback;
drop policy if exists "authenticated_insert_feedback" on public.feedback;
drop policy if exists "authenticated_update_feedback" on public.feedback;
drop policy if exists "authenticated_delete_feedback" on public.feedback;
drop policy if exists "service_role_all_feedback" on public.feedback; 