-- Migration: Add cost information to logs table
-- Description: Adds columns to track cost information for page generation
-- Created at: 2025-06-13 00:00:00 UTC

-- Add cost columns to logs table
ALTER TABLE public.logs 
ADD COLUMN prompt_tokens INTEGER,
ADD COLUMN completion_tokens INTEGER,
ADD COLUMN total_tokens INTEGER,
ADD COLUMN prompt_cost DECIMAL(10, 6),
ADD COLUMN completion_cost DECIMAL(10, 6),
ADD COLUMN total_cost DECIMAL(10, 6),
ADD COLUMN cost_currency TEXT DEFAULT 'USD',
ADD COLUMN model TEXT,
ADD COLUMN generation_attempts INTEGER DEFAULT 1,
ADD COLUMN cumulative_tokens INTEGER,
ADD COLUMN cumulative_cost DECIMAL(10, 6);

-- Add comments to explain new columns
COMMENT ON COLUMN public.logs.prompt_tokens IS 'Number of prompt tokens used in the generation';
COMMENT ON COLUMN public.logs.completion_tokens IS 'Number of completion tokens used in the generation';
COMMENT ON COLUMN public.logs.total_tokens IS 'Total number of tokens used in the generation';
COMMENT ON COLUMN public.logs.prompt_cost IS 'Cost of prompt tokens in the currency specified by cost_currency';
COMMENT ON COLUMN public.logs.completion_cost IS 'Cost of completion tokens in the currency specified by cost_currency';
COMMENT ON COLUMN public.logs.total_cost IS 'Total cost of generation in the currency specified by cost_currency';
COMMENT ON COLUMN public.logs.cost_currency IS 'Currency for cost values, defaults to USD';
COMMENT ON COLUMN public.logs.model IS 'Model used for the generation';
COMMENT ON COLUMN public.logs.generation_attempts IS 'Number of attempts made to generate the page';
COMMENT ON COLUMN public.logs.cumulative_tokens IS 'Total tokens used across all generation attempts';
COMMENT ON COLUMN public.logs.cumulative_cost IS 'Total cost across all generation attempts'; 