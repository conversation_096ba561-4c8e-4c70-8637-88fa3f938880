-- Migration: Initial Schema for Landing Page Now MVP
-- Description: Creates the base database schema including enum types, tables, relationships, indexes and RLS policies
-- Created at: 2025-04-15 08:27:03 UTC

-- 1. Ensure required extensions are enabled
create extension if not exists "uuid-ossp";

-- 2. Enum Types
create type public.generation_status as enum (
  'PENDING',
  'SUCCESS',
  'FAILED'
);

-- 3. Tables Definition
create table public.pages (
  id bigserial not null,
  user_id uuid not null,
  parent_page_id bigint null,
  config jsonb not null,
  html_content text not null,
  created_at timestamp with time zone not null default now(),
  constraint pages_pkey primary key (id),
  constraint pages_user_id_fkey foreign key (user_id) references auth.users(id) on delete cascade,
  constraint pages_parent_page_id_fkey foreign key (parent_page_id) references public.pages(id) on delete cascade
);
comment on table public.pages is 'Stores landing page content and configurations, including versions.';
comment on column public.pages.id is 'Unique identifier for each page version (auto-incrementing).';
comment on column public.pages.user_id is 'Foreign key referencing the user who owns the page.';
comment on column public.pages.parent_page_id is 'Foreign key referencing the parent page version (NULL for the initial version).';
comment on column public.pages.config is 'JSONB storing page configuration options (e.g., target audience, keywords, style, integrations).';
comment on column public.pages.html_content is 'Stores the generated HTML/CSS/JS content for the page.';
comment on column public.pages.created_at is 'Timestamp of when the page version was created.';

create table public.logs (
  id uuid not null default uuid_generate_v4(),
  user_id uuid not null,
  page_id bigint not null,
  generation_duration_ms integer null,
  status public.generation_status not null,
  error_message text null,
  created_at timestamp with time zone not null default now(),
  constraint logs_pkey primary key (id),
  constraint logs_user_id_fkey foreign key (user_id) references auth.users(id) on delete cascade,
  constraint logs_page_id_fkey foreign key (page_id) references public.pages(id) on delete cascade
);
comment on table public.logs is 'Stores logs for each page generation attempt.';
comment on column public.logs.id is 'Unique identifier for each log entry.';
comment on column public.logs.user_id is 'Foreign key referencing the user who initiated the generation.';
comment on column public.logs.page_id is 'Foreign key referencing the specific page version being generated.';
comment on column public.logs.generation_duration_ms is 'Duration of the generation process in milliseconds.';
comment on column public.logs.status is 'Status of the generation attempt (PENDING, SUCCESS, FAILED).';
comment on column public.logs.error_message is 'Detailed error message if the generation failed.';
comment on column public.logs.created_at is 'Timestamp of when the log entry was created.';

create table public.feedback (
  id uuid not null default uuid_generate_v4(),
  user_id uuid not null,
  page_id bigint not null,
  rating boolean not null,
  comment text null,
  created_at timestamp with time zone not null default now(),
  constraint feedback_pkey primary key (id),
  constraint feedback_user_id_fkey foreign key (user_id) references auth.users(id) on delete cascade,
  constraint feedback_page_id_fkey foreign key (page_id) references public.pages(id) on delete cascade
);
comment on table public.feedback is 'Stores user feedback provided for generated pages.';
comment on column public.feedback.id is 'Unique identifier for each feedback entry.';
comment on column public.feedback.user_id is 'Foreign key referencing the user who provided the feedback.';
comment on column public.feedback.page_id is 'Foreign key referencing the specific page version the feedback is for.';
comment on column public.feedback.rating is 'User rating (e.g., true for positive, false for negative).';
comment on column public.feedback.comment is 'Optional textual comment provided by the user.';
comment on column public.feedback.created_at is 'Timestamp of when the feedback was submitted.';

-- 4. Indexes
-- Primary key indexes are created automatically
-- Create indexes for foreign keys and frequently queried columns
create index idx_pages_user_id on public.pages using btree (user_id);
create index idx_pages_parent_page_id on public.pages using btree (parent_page_id);
create index idx_logs_user_id on public.logs using btree (user_id);
create index idx_logs_page_id on public.logs using btree (page_id);
create index idx_feedback_user_id on public.feedback using btree (user_id);
create index idx_feedback_page_id on public.feedback using btree (page_id);

-- Indexes on frequently queried timestamp columns
create index idx_pages_created_at on public.pages using btree (created_at);
create index idx_logs_created_at on public.logs using btree (created_at);
create index idx_feedback_created_at on public.feedback using btree (created_at);

-- 5. Row Level Security (RLS) Policies
-- Enable RLS for all tables
alter table public.pages enable row level security;
alter table public.logs enable row level security;
alter table public.feedback enable row level security;

-- Policies for 'pages' table
-- Allow authenticated users to view their own pages
create policy "authenticated_select_pages" 
  on public.pages 
  for select 
  to authenticated 
  using (auth.uid() = user_id);

-- Allow authenticated users to insert their own pages
create policy "authenticated_insert_pages" 
  on public.pages 
  for insert 
  to authenticated 
  with check (auth.uid() = user_id);

-- Allow authenticated users to update their own pages
create policy "authenticated_update_pages" 
  on public.pages 
  for update 
  to authenticated 
  using (auth.uid() = user_id) 
  with check (auth.uid() = user_id);

-- Allow authenticated users to delete their own pages
create policy "authenticated_delete_pages" 
  on public.pages 
  for delete 
  to authenticated 
  using (auth.uid() = user_id);

-- Policies for 'logs' table
-- Allow authenticated users to view their own logs
create policy "authenticated_select_logs" 
  on public.logs 
  for select 
  to authenticated 
  using (auth.uid() = user_id);

-- Allow authenticated users to insert their own logs
create policy "authenticated_insert_logs" 
  on public.logs 
  for insert 
  to authenticated 
  with check (auth.uid() = user_id);

-- Allow authenticated users to update their own logs
create policy "authenticated_update_logs" 
  on public.logs 
  for update 
  to authenticated 
  using (auth.uid() = user_id) 
  with check (auth.uid() = user_id);

-- Allow authenticated users to delete their own logs
create policy "authenticated_delete_logs" 
  on public.logs 
  for delete 
  to authenticated 
  using (auth.uid() = user_id);

-- Policies for 'feedback' table
-- Allow authenticated users to view their own feedback
create policy "authenticated_select_feedback" 
  on public.feedback 
  for select 
  to authenticated 
  using (auth.uid() = user_id);

-- Allow authenticated users to insert their own feedback
create policy "authenticated_insert_feedback" 
  on public.feedback 
  for insert 
  to authenticated 
  with check (auth.uid() = user_id);

-- Allow authenticated users to update their own feedback
create policy "authenticated_update_feedback" 
  on public.feedback 
  for update 
  to authenticated 
  using (auth.uid() = user_id) 
  with check (auth.uid() = user_id);

-- Allow authenticated users to delete their own feedback
create policy "authenticated_delete_feedback" 
  on public.feedback 
  for delete 
  to authenticated 
  using (auth.uid() = user_id);

-- Service role policies (full access)
create policy "service_role_all_pages" 
  on public.pages 
  for all 
  to service_role 
  using (true) 
  with check (true);

create policy "service_role_all_logs" 
  on public.logs 
  for all 
  to service_role 
  using (true) 
  with check (true);

create policy "service_role_all_feedback" 
  on public.feedback 
  for all 
  to service_role 
  using (true) 
  with check (true);