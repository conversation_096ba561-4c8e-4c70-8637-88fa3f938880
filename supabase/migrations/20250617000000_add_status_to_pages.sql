-- Migration: Add status field to pages table
-- Description: Adds the status field to pages table to track page generation status
-- Created at: 2025-06-17 00:00:00 UTC

-- Add status column to pages table with default value PENDING
ALTER TABLE public.pages 
ADD COLUMN status public.generation_status NOT NULL DEFAULT 'PENDING';

-- Add comment to explain the column
COMMENT ON COLUMN public.pages.status IS 'Current status of the page (PENDING, SUCCESS, FAILED)';

-- Create a function to update the pages status from logs
CREATE OR REPLACE FUNCTION update_page_status_from_logs()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the corresponding page status when a log is added
  UPDATE public.pages
  SET status = NEW.status
  WHERE id = NEW.page_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update page status whenever a log is added
CREATE TRIGGER update_page_status
AFTER INSERT ON public.logs
FOR EACH ROW
EXECUTE FUNCTION update_page_status_from_logs(); 