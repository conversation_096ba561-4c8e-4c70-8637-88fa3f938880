-- Migration: Clean up cost tracking fields
-- Description: Removes redundant fields from cost tracking now that we're using API-provided costs
-- Created at: 2025-06-14 00:00:00 UTC

-- We're retaining all the existing fields to maintain compatibility
-- with existing records, but adding comments to clarify the source of truth

COMMENT ON COLUMN public.logs.total_cost IS 'Total cost of generation in USD, directly from OpenRouter API';
COMMENT ON COLUMN public.logs.prompt_cost IS 'Cost of prompt tokens in USD if available from API, otherwise 0';
COMMENT ON COLUMN public.logs.completion_cost IS 'Cost of completion tokens in USD if available from API, otherwise 0';
COMMENT ON COLUMN public.logs.cumulative_cost IS 'Total cost across all generation attempts, sum of API-provided costs'; 